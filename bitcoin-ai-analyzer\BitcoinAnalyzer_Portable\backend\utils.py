"""
Bitcoin Data Fetcher - جلب بيانات البتكوين من Binance API
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BitcoinDataFetcher:
    """فئة لجلب بيانات البتكوين من Binance API"""
    
    def __init__(self):
        self.base_url = "https://api.binance.com/api/v3"
        self.symbol = "BTCUSDT"
        
    def get_current_price(self):
        """جلب السعر الحالي للبتكوين"""
        try:
            url = f"{self.base_url}/ticker/price"
            params = {"symbol": self.symbol}
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return float(data['price'])
            
        except requests.exceptions.RequestException as e:
            logger.error(f"خطأ في جلب السعر الحالي: {e}")
            return None
        except Exception as e:
            logger.error(f"خطأ غير متوقع: {e}")
            return None
    
    def get_24h_stats(self):
        """جلب إحصائيات 24 ساعة"""
        try:
            url = f"{self.base_url}/ticker/24hr"
            params = {"symbol": self.symbol}
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return {
                'price_change': float(data['priceChange']),
                'price_change_percent': float(data['priceChangePercent']),
                'high_price': float(data['highPrice']),
                'low_price': float(data['lowPrice']),
                'volume': float(data['volume']),
                'quote_volume': float(data['quoteVolume'])
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"خطأ في جلب إحصائيات 24 ساعة: {e}")
            return None
        except Exception as e:
            logger.error(f"خطأ غير متوقع: {e}")
            return None
    
    def get_klines(self, interval="1h", limit=1000):
        """
        جلب بيانات الشموع اليابانية
        
        Args:
            interval: الفترة الزمنية (1m, 5m, 15m, 30m, 1h, 4h, 1d)
            limit: عدد الشموع (حد أقصى 1000)
        """
        try:
            url = f"{self.base_url}/klines"
            params = {
                "symbol": self.symbol,
                "interval": interval,
                "limit": limit
            }
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # تحويل البيانات إلى DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # تحويل الأنواع
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            
            # تحويل الوقت
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('datetime', inplace=True)
            
            # الاحتفاظ بالأعمدة المهمة فقط
            df = df[['open', 'high', 'low', 'close', 'volume']]
            
            logger.info(f"تم جلب {len(df)} شمعة بنجاح للفترة {interval}")
            return df
            
        except requests.exceptions.RequestException as e:
            logger.error(f"خطأ في جلب بيانات الشموع: {e}")
            return None
        except Exception as e:
            logger.error(f"خطأ غير متوقع في معالجة البيانات: {e}")
            return None
    
    def get_multiple_timeframes(self):
        """جلب بيانات من عدة فترات زمنية"""
        timeframes = {
            '1h': self.get_klines('1h', 500),
            '4h': self.get_klines('4h', 250),
            '1d': self.get_klines('1d', 100)
        }
        
        # التأكد من نجاح جلب البيانات
        valid_timeframes = {}
        for tf, data in timeframes.items():
            if data is not None and not data.empty:
                valid_timeframes[tf] = data
            else:
                logger.warning(f"فشل في جلب بيانات الفترة {tf}")
        
        return valid_timeframes
    
    def validate_data(self, df):
        """التحقق من صحة البيانات"""
        if df is None or df.empty:
            return False
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            return False
        
        # التحقق من وجود قيم فارغة
        if df[required_columns].isnull().any().any():
            logger.warning("توجد قيم فارغة في البيانات")
            return False
        
        # التحقق من منطقية الأسعار
        if (df['high'] < df['low']).any() or (df['high'] < df['close']).any() or (df['low'] > df['close']).any():
            logger.warning("توجد قيم غير منطقية في الأسعار")
            return False
        
        return True

def test_data_fetcher():
    """اختبار جلب البيانات"""
    fetcher = BitcoinDataFetcher()
    
    print("🔍 اختبار جلب البيانات...")
    
    # اختبار السعر الحالي
    current_price = fetcher.get_current_price()
    if current_price:
        print(f"💰 السعر الحالي: ${current_price:,.2f}")
    else:
        print("❌ فشل في جلب السعر الحالي")
    
    # اختبار إحصائيات 24 ساعة
    stats_24h = fetcher.get_24h_stats()
    if stats_24h:
        print(f"📊 التغيير خلال 24 ساعة: {stats_24h['price_change_percent']:.2f}%")
    else:
        print("❌ فشل في جلب إحصائيات 24 ساعة")
    
    # اختبار بيانات الشموع
    klines = fetcher.get_klines('1h', 100)
    if klines is not None and not klines.empty:
        print(f"📈 تم جلب {len(klines)} شمعة بنجاح")
        print(f"📅 من {klines.index[0]} إلى {klines.index[-1]}")
    else:
        print("❌ فشل في جلب بيانات الشموع")

if __name__ == "__main__":
    test_data_fetcher()
