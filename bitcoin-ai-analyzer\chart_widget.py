#!/usr/bin/env python3
"""
Bitcoin Chart Widget
مكون الرسم البياني لمحلل البتكوين الذكي
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# إضافة مجلد backend إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

class BitcoinChartWidget:
    """مكون الرسم البياني للبتكوين"""
    
    def __init__(self, parent, data_fetcher):
        self.parent = parent
        self.data_fetcher = data_fetcher
        self.current_timeframe = '1h'
        
        # إعداد matplotlib للعربية
        plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
        
        self.setup_chart_frame()
        self.create_chart()
        
    def setup_chart_frame(self):
        """إعداد إطار الرسم البياني"""
        # إطار التحكم
        control_frame = ttk.Frame(self.parent)
        control_frame.pack(fill='x', pady=(0, 10))
        
        # عنوان القسم
        ttk.Label(control_frame, text="📊 الرسم البياني", 
                 font=('Arial', 12, 'bold')).pack(side='left')
        
        # أدوات التحكم
        controls_right = ttk.Frame(control_frame)
        controls_right.pack(side='right')
        
        # اختيار الفترة الزمنية
        ttk.Label(controls_right, text="الفترة:").pack(side='left', padx=(0, 5))
        
        self.timeframe_var = tk.StringVar(value=self.current_timeframe)
        timeframe_combo = ttk.Combobox(controls_right, textvariable=self.timeframe_var,
                                      values=['1m', '5m', '15m', '1h', '4h', '1d'],
                                      state='readonly', width=8)
        timeframe_combo.pack(side='left', padx=(0, 10))
        timeframe_combo.bind('<<ComboboxSelected>>', self.on_timeframe_change)
        
        # زر التحديث
        refresh_chart_btn = ttk.Button(controls_right, text="🔄 تحديث الرسم",
                                      command=self.refresh_chart)
        refresh_chart_btn.pack(side='left')
        
        # إطار الرسم البياني
        self.chart_frame = ttk.Frame(self.parent)
        self.chart_frame.pack(fill='both', expand=True)
    
    def create_chart(self):
        """إنشاء الرسم البياني"""
        # إنشاء Figure
        self.fig = Figure(figsize=(12, 6), dpi=100, facecolor='white')
        self.ax = self.fig.add_subplot(111)
        
        # إعداد Canvas
        self.canvas = FigureCanvasTkAgg(self.fig, self.chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill='both', expand=True)
        
        # شريط الأدوات
        toolbar_frame = ttk.Frame(self.chart_frame)
        toolbar_frame.pack(fill='x')
        
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()
        
        # رسم البيانات الأولية
        self.plot_initial_data()
    
    def plot_initial_data(self):
        """رسم البيانات الأولية"""
        try:
            # جلب البيانات
            df = self.data_fetcher.get_klines(self.current_timeframe, 100)
            
            if df is not None and not df.empty:
                self.plot_candlestick_chart(df)
            else:
                self.plot_placeholder()
                
        except Exception as e:
            print(f"خطأ في رسم البيانات الأولية: {e}")
            self.plot_placeholder()
    
    def plot_candlestick_chart(self, df):
        """رسم الشموع اليابانية"""
        self.ax.clear()
        
        # تحضير البيانات
        dates = df.index
        opens = df['open']
        highs = df['high']
        lows = df['low']
        closes = df['close']
        volumes = df['volume']
        
        # رسم الشموع
        for i in range(len(df)):
            date = dates[i]
            open_price = opens[i]
            high_price = highs[i]
            low_price = lows[i]
            close_price = closes[i]
            
            # تحديد اللون
            color = '#26a69a' if close_price >= open_price else '#ef5350'
            
            # رسم الخط العمودي (high-low)
            self.ax.plot([i, i], [low_price, high_price], color='black', linewidth=1)
            
            # رسم جسم الشمعة
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            
            rect = plt.Rectangle((i-0.3, body_bottom), 0.6, body_height,
                               facecolor=color, edgecolor='black', linewidth=0.5)
            self.ax.add_patch(rect)
        
        # إضافة المتوسطات المتحركة
        self.add_moving_averages(df)
        
        # تنسيق الرسم البياني
        self.format_chart(df)
    
    def add_moving_averages(self, df):
        """إضافة المتوسطات المتحركة"""
        try:
            # حساب المتوسطات المتحركة
            ma20 = df['close'].rolling(window=20).mean()
            ma50 = df['close'].rolling(window=50).mean()
            
            # رسم المتوسطات
            x_values = range(len(df))
            
            self.ax.plot(x_values, ma20, color='#2196F3', linewidth=2, 
                        label='MA 20', alpha=0.8)
            self.ax.plot(x_values, ma50, color='#FF9800', linewidth=2, 
                        label='MA 50', alpha=0.8)
            
            # إضافة وسيلة الإيضاح
            self.ax.legend(loc='upper left')
            
        except Exception as e:
            print(f"خطأ في إضافة المتوسطات المتحركة: {e}")
    
    def format_chart(self, df):
        """تنسيق الرسم البياني"""
        # العنوان
        current_price = df['close'].iloc[-1]
        change = df['close'].iloc[-1] - df['close'].iloc[-2]
        change_percent = (change / df['close'].iloc[-2]) * 100
        
        title = f"BTC/USDT - {self.current_timeframe.upper()} | "
        title += f"${current_price:,.2f} ({change_percent:+.2f}%)"
        
        self.ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
        
        # تسميات المحاور
        self.ax.set_ylabel('السعر (USDT)', fontsize=12)
        self.ax.set_xlabel('الوقت', fontsize=12)
        
        # تنسيق محور X (التواريخ)
        num_ticks = min(10, len(df))
        tick_indices = np.linspace(0, len(df)-1, num_ticks, dtype=int)
        tick_labels = [df.index[i].strftime('%H:%M\n%m/%d') for i in tick_indices]
        
        self.ax.set_xticks(tick_indices)
        self.ax.set_xticklabels(tick_labels, rotation=45, ha='right')
        
        # تنسيق الشبكة
        self.ax.grid(True, alpha=0.3, linestyle='--')
        
        # تنسيق المحاور
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        
        # ضبط التخطيط
        self.fig.tight_layout()
        
        # تحديث الرسم
        self.canvas.draw()
    
    def plot_placeholder(self):
        """رسم مكان مؤقت عند عدم توفر البيانات"""
        self.ax.clear()
        self.ax.text(0.5, 0.5, '📊 جاري تحميل البيانات...\nأو تحقق من اتصال الإنترنت',
                    horizontalalignment='center', verticalalignment='center',
                    transform=self.ax.transAxes, fontsize=14,
                    bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        self.ax.set_xlim(0, 1)
        self.ax.set_ylim(0, 1)
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        
        self.canvas.draw()
    
    def on_timeframe_change(self, event=None):
        """معالج تغيير الفترة الزمنية"""
        self.current_timeframe = self.timeframe_var.get()
        self.refresh_chart()
    
    def refresh_chart(self):
        """تحديث الرسم البياني"""
        try:
            # جلب البيانات الجديدة
            limit = 200 if self.current_timeframe in ['1m', '5m'] else 100
            df = self.data_fetcher.get_klines(self.current_timeframe, limit)
            
            if df is not None and not df.empty:
                self.plot_candlestick_chart(df)
            else:
                self.plot_placeholder()
                
        except Exception as e:
            print(f"خطأ في تحديث الرسم البياني: {e}")
            self.plot_placeholder()
    
    def update_chart_data(self, new_data=None):
        """تحديث بيانات الرسم البياني"""
        if new_data is not None:
            self.plot_candlestick_chart(new_data)
        else:
            self.refresh_chart()
    
    def set_dark_theme(self):
        """تطبيق الثيم المظلم"""
        self.fig.patch.set_facecolor('#2d2d2d')
        self.ax.set_facecolor('#2d2d2d')
        self.ax.tick_params(colors='white')
        self.ax.xaxis.label.set_color('white')
        self.ax.yaxis.label.set_color('white')
        self.ax.title.set_color('white')
        self.canvas.draw()
    
    def set_light_theme(self):
        """تطبيق الثيم الفاتح"""
        self.fig.patch.set_facecolor('white')
        self.ax.set_facecolor('white')
        self.ax.tick_params(colors='black')
        self.ax.xaxis.label.set_color('black')
        self.ax.yaxis.label.set_color('black')
        self.ax.title.set_color('black')
        self.canvas.draw()

def test_chart_widget():
    """اختبار مكون الرسم البياني"""
    from utils import BitcoinDataFetcher
    
    root = tk.Tk()
    root.title("اختبار الرسم البياني")
    root.geometry("800x600")
    
    fetcher = BitcoinDataFetcher()
    chart = BitcoinChartWidget(root, fetcher)
    
    root.mainloop()

if __name__ == "__main__":
    test_chart_widget()
