# مولد الشهادات الرقمية - Certificate Generator

أداة GUI بلغة Python لإنشاء شهادات التوقيع الرقمية ذاتية التوقيع بصيغة .p12

## المميزات

- واجهة مستخدم رسومية سهلة الاستخدام
- إنشاء شهادات X.509 ذاتية التوقيع
- تصدير بصيغة .p12 محمية بكلمة مرور
- دعم تخصيص جميع بيانات الشهادة
- البحث التلقائي عن OpenSSL
- خيارات إضافية مفيدة

## المتطلبات

### البرمجيات المطلوبة:
- Python 3.6 أو أحدث
- OpenSSL مثبت على النظام

### تحميل OpenSSL لنظام Windows:
1. قم بتحميل OpenSSL من: https://slproweb.com/products/Win32OpenSSL.html
2. اختر النسخة المناسبة (Win64 OpenSSL v3.x.x)
3. قم بالتثبيت في المسار الافتراضي: `C:\OpenSSL-Win64\`

## طريقة الاستخدام

### تشغيل الأداة:
```bash
python main.py
```

### خطوات إنشاء الشهادة:

1. **ملء البيانات الأساسية:**
   - الاسم الكامل (مطلوب)
   - الدولة (افتراضي: SA)
   - المنطقة/الولاية
   - المدينة
   - المؤسسة
   - البريد الإلكتروني

2. **تحديد إعدادات الشهادة:**
   - مدة الصلاحية بالأيام (افتراضي: 365)
   - كلمة مرور الشهادة (مطلوبة)

3. **تحديد مسار OpenSSL:**
   - الأداة تبحث تلقائياً عن OpenSSL
   - يمكن تحديد المسار يدوياً إذا لزم الأمر

4. **اختيار مجلد الحفظ:**
   - افتراضياً: سطح المكتب
   - يمكن تغييره حسب الحاجة

5. **الخيارات الإضافية:**
   - فتح المجلد بعد الإنشاء
   - حذف الملفات المؤقتة

6. **إنشاء الشهادة:**
   - اضغط على زر "إنشاء الشهادة"
   - انتظر حتى اكتمال العملية

## استخدام الشهادة المُنشأة

### على iPhone/iPad:
1. أرسل ملف .p12 إلى الجهاز عبر البريد أو AirDrop
2. اضغط على الملف لتثبيته
3. أدخل كلمة المرور
4. اذهب إلى الإعدادات > عام > VPN وإدارة الجهاز
5. قم بتفعيل الشهادة

### على Mac:
1. اضغط مرتين على ملف .p12
2. أدخل كلمة المرور
3. ستُضاف الشهادة إلى Keychain Access

### في برامج البريد:
- يمكن استيراد الشهادة في Outlook، Thunderbird، وغيرها
- استخدم كلمة المرور المحددة أثناء الإنشاء

## استكشاف الأخطاء

### "لم يتم العثور على OpenSSL":
- تأكد من تثبيت OpenSSL
- حدد المسار يدوياً: `C:\OpenSSL-Win64\bin\openssl.exe`

### "فشل في إنشاء المفتاح الخاص":
- تأكد من صحة مسار OpenSSL
- تأكد من وجود صلاحيات الكتابة في المجلد المؤقت

### "فشل في إنشاء الشهادة":
- تحقق من صحة البيانات المدخلة
- تأكد من عدم وجود رموز خاصة في الحقول

## الملفات المُنشأة

- `اسم_الشخص_certificate.p12`: الشهادة النهائية
- في حالة وجود ملف بنفس الاسم، سيُضاف رقم تسلسلي

## الأمان

- احتفظ بكلمة مرور الشهادة في مكان آمن
- لا تشارك ملف .p12 مع أشخاص غير مخولين
- استخدم كلمات مرور قوية

## الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع المطور.

---

**ملاحظة:** هذه الأداة تنشئ شهادات ذاتية التوقيع للاستخدام الشخصي أو التطوير. للاستخدام التجاري، يُنصح بالحصول على شهادات من جهات معتمدة.
