#!/usr/bin/env python3
"""
Bitcoin AI Analyzer - Desktop Application
محلل البتكوين الذكي - تطبيق سطح المكتب

تطبيق مستقل بواجهة رسومية مدمجة بدون الحاجة لمتصفح
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import threading
import time
from datetime import datetime
import sys
import os

# إضافة مجلد backend إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

# استيراد وحدات التحليل
from utils import BitcoinDataFetcher
from analysis import TechnicalAnalyzer
from signals import TradingSignalGenerator

# استيراد مكون الرسم البياني
try:
    from chart_widget import BitcoinChartWidget
    CHART_AVAILABLE = True
except ImportError:
    CHART_AVAILABLE = False
    print("⚠️ matplotlib غير متوفر - سيتم تعطيل الرسوم البيانية")

# استيراد مدير الإعدادات
try:
    from settings_manager import SettingsManager, SettingsWindow
    SETTINGS_AVAILABLE = True
except ImportError:
    SETTINGS_AVAILABLE = False
    print("⚠️ مدير الإعدادات غير متوفر")

class BitcoinAnalyzerGUI:
    """الواجهة الرسومية الرئيسية لمحلل البتكوين الذكي"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        
        # متغيرات البيانات
        self.data_fetcher = BitcoinDataFetcher()
        self.signal_generator = TradingSignalGenerator()
        self.current_data = {}

        # إعداد مدير الإعدادات
        if SETTINGS_AVAILABLE:
            self.settings_manager = SettingsManager()
            self.is_dark_mode = self.settings_manager.get('theme') == 'dark'
            self.auto_refresh = self.settings_manager.get('auto_refresh')
        else:
            self.settings_manager = None
            self.is_dark_mode = False
            self.auto_refresh = True

        # تطبيق الثيم المحفوظ
        if self.is_dark_mode:
            self.apply_dark_theme()

        # بدء التحديث التلقائي
        self.start_auto_refresh()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("محلل البتكوين الذكي - Bitcoin AI Analyzer")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        
        # تعيين الأيقونة إذا كانت متوفرة
        try:
            self.root.iconbitmap("bitcoin_icon.ico")
        except:
            pass
        
        # إعداد الخروج
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_styles(self):
        """إعداد الأنماط والألوان"""
        self.style = ttk.Style()
        
        # ألوان البتكوين
        self.colors = {
            'bitcoin_orange': '#F7931A',
            'bitcoin_dark': '#4D4D4D',
            'success': '#28a745',
            'danger': '#dc3545',
            'warning': '#ffc107',
            'info': '#17a2b8',
            'light_bg': '#ffffff',
            'dark_bg': '#2d2d2d',
            'light_text': '#212529',
            'dark_text': '#ffffff'
        }
        
        # خطوط
        self.fonts = {
            'title': font.Font(family="Arial", size=16, weight="bold"),
            'subtitle': font.Font(family="Arial", size=12, weight="bold"),
            'normal': font.Font(family="Arial", size=10),
            'small': font.Font(family="Arial", size=9)
        }
        
        self.apply_light_theme()
    
    def apply_light_theme(self):
        """تطبيق الثيم الفاتح"""
        self.root.configure(bg=self.colors['light_bg'])
        
        # تكوين أنماط ttk
        self.style.configure('Title.TLabel', 
                           font=self.fonts['title'],
                           background=self.colors['light_bg'],
                           foreground=self.colors['bitcoin_dark'])
        
        self.style.configure('Subtitle.TLabel',
                           font=self.fonts['subtitle'],
                           background=self.colors['light_bg'],
                           foreground=self.colors['bitcoin_dark'])
        
        self.style.configure('Normal.TLabel',
                           font=self.fonts['normal'],
                           background=self.colors['light_bg'],
                           foreground=self.colors['light_text'])
    
    def apply_dark_theme(self):
        """تطبيق الثيم المظلم"""
        self.root.configure(bg=self.colors['dark_bg'])
        
        self.style.configure('Title.TLabel',
                           font=self.fonts['title'],
                           background=self.colors['dark_bg'],
                           foreground=self.colors['dark_text'])
        
        self.style.configure('Subtitle.TLabel',
                           font=self.fonts['subtitle'],
                           background=self.colors['dark_bg'],
                           foreground=self.colors['dark_text'])
        
        self.style.configure('Normal.TLabel',
                           font=self.fonts['normal'],
                           background=self.colors['dark_bg'],
                           foreground=self.colors['dark_text'])
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط العنوان والأدوات
        self.create_header(main_frame)
        
        # الإطار الرئيسي للمحتوى
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill='both', expand=True, pady=(10, 0))
        
        # تقسيم المحتوى إلى أعمدة
        self.create_left_panel(content_frame)
        self.create_right_panel(content_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان والأدوات"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill='x', pady=(0, 10))
        
        # العنوان الرئيسي
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(side='left')
        
        title_label = ttk.Label(title_frame, text="🚀 محلل البتكوين الذكي", 
                               style='Title.TLabel')
        title_label.pack(anchor='w')
        
        subtitle_label = ttk.Label(title_frame, text="Bitcoin AI Analyzer - تحليل فني احترافي",
                                  style='Normal.TLabel')
        subtitle_label.pack(anchor='w')
        
        # أدوات التحكم
        controls_frame = ttk.Frame(header_frame)
        controls_frame.pack(side='right')
        
        # زر الوضع الليلي
        self.theme_btn = ttk.Button(controls_frame, text="🌙 وضع ليلي",
                                   command=self.toggle_theme)
        self.theme_btn.pack(side='right', padx=(5, 0))
        
        # زر الإعدادات
        if SETTINGS_AVAILABLE:
            self.settings_btn = ttk.Button(controls_frame, text="⚙️ إعدادات",
                                          command=self.open_settings)
            self.settings_btn.pack(side='right', padx=(5, 0))

        # زر التحديث
        self.refresh_btn = ttk.Button(controls_frame, text="🔄 حلل الآن",
                                     command=self.manual_refresh)
        self.refresh_btn.pack(side='right', padx=(5, 0))

        # مؤشر الحالة
        self.status_label = ttk.Label(controls_frame, text="جاري التحميل...",
                                     style='Normal.TLabel')
        self.status_label.pack(side='right', padx=(5, 0))
    
    def create_left_panel(self, parent):
        """إنشاء اللوحة اليسرى (الأسعار والتوصيات)"""
        left_frame = ttk.Frame(parent)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))
        
        # قسم الأسعار
        self.create_price_section(left_frame)
        
        # قسم التوصيات
        self.create_recommendation_section(left_frame)
        
        # قسم تاريخ التحليلات
        self.create_history_section(left_frame)
    
    def create_right_panel(self, parent):
        """إنشاء اللوحة اليمنى (المؤشرات والرسوم البيانية)"""
        right_frame = ttk.Frame(parent)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # قسم المؤشرات الفنية
        self.create_indicators_section(right_frame)
        
        # قسم الرسم البياني (سيتم إضافته لاحقاً)
        self.create_chart_placeholder(right_frame)
    
    def create_price_section(self, parent):
        """إنشاء قسم عرض الأسعار"""
        price_frame = ttk.LabelFrame(parent, text="💰 السعر الحالي", padding=10)
        price_frame.pack(fill='x', pady=(0, 10))
        
        # السعر الرئيسي
        self.price_label = ttk.Label(price_frame, text="$--,---", 
                                    font=self.fonts['title'])
        self.price_label.pack()
        
        # التغيير في 24 ساعة
        self.change_label = ttk.Label(price_frame, text="التغيير: --%")
        self.change_label.pack()
        
        # إحصائيات إضافية
        stats_frame = ttk.Frame(price_frame)
        stats_frame.pack(fill='x', pady=(10, 0))
        
        # أعلى وأقل سعر
        self.high_label = ttk.Label(stats_frame, text="أعلى: $--")
        self.high_label.pack(side='left')
        
        self.low_label = ttk.Label(stats_frame, text="أقل: $--")
        self.low_label.pack(side='right')
        
        # آخر تحديث
        self.update_time_label = ttk.Label(price_frame, text="آخر تحديث: --",
                                          style='Normal.TLabel')
        self.update_time_label.pack(pady=(5, 0))
    
    def create_recommendation_section(self, parent):
        """إنشاء قسم التوصيات"""
        rec_frame = ttk.LabelFrame(parent, text="🎯 التوصية الحالية", padding=10)
        rec_frame.pack(fill='x', pady=(0, 10))
        
        # التوصية الرئيسية
        self.recommendation_label = ttk.Label(rec_frame, text="انتظار",
                                            font=self.fonts['subtitle'])
        self.recommendation_label.pack()
        
        # مستوى الثقة
        confidence_frame = ttk.Frame(rec_frame)
        confidence_frame.pack(fill='x', pady=(10, 0))
        
        ttk.Label(confidence_frame, text="مستوى الثقة:").pack(side='left')
        self.confidence_label = ttk.Label(confidence_frame, text="--%")
        self.confidence_label.pack(side='right')
        
        # شريط الثقة
        self.confidence_progress = ttk.Progressbar(rec_frame, length=200, mode='determinate')
        self.confidence_progress.pack(pady=(5, 10))
        
        # التفسير
        self.explanation_text = tk.Text(rec_frame, height=4, wrap='word',
                                       font=self.fonts['small'])
        self.explanation_text.pack(fill='x')
        
        # معنويات السوق
        sentiment_frame = ttk.Frame(rec_frame)
        sentiment_frame.pack(fill='x', pady=(10, 0))
        
        ttk.Label(sentiment_frame, text="معنويات السوق:").pack(side='left')
        self.sentiment_label = ttk.Label(sentiment_frame, text="😐 محايد")
        self.sentiment_label.pack(side='right')

    def create_indicators_section(self, parent):
        """إنشاء قسم المؤشرات الفنية"""
        indicators_frame = ttk.LabelFrame(parent, text="📊 المؤشرات الفنية", padding=10)
        indicators_frame.pack(fill='both', expand=True, pady=(0, 10))

        # إنشاء notebook للمؤشرات
        self.indicators_notebook = ttk.Notebook(indicators_frame)
        self.indicators_notebook.pack(fill='both', expand=True)

        # تبويب RSI
        self.create_rsi_tab()

        # تبويب MACD
        self.create_macd_tab()

        # تبويب EMA
        self.create_ema_tab()

        # تبويب Bollinger Bands
        self.create_bollinger_tab()

        # تبويب Volume
        self.create_volume_tab()

    def create_rsi_tab(self):
        """إنشاء تبويب مؤشر RSI"""
        rsi_frame = ttk.Frame(self.indicators_notebook)
        self.indicators_notebook.add(rsi_frame, text="RSI")

        # قيمة RSI
        value_frame = ttk.Frame(rsi_frame)
        value_frame.pack(fill='x', pady=10)

        ttk.Label(value_frame, text="القيمة:", font=self.fonts['subtitle']).pack(side='left')
        self.rsi_value_label = ttk.Label(value_frame, text="--", font=self.fonts['subtitle'])
        self.rsi_value_label.pack(side='right')

        # شريط RSI
        self.rsi_progress = ttk.Progressbar(rsi_frame, length=300, mode='determinate')
        self.rsi_progress.pack(pady=10)

        # مناطق RSI
        zones_frame = ttk.Frame(rsi_frame)
        zones_frame.pack(fill='x')

        ttk.Label(zones_frame, text="30", foreground=self.colors['success']).pack(side='left')
        ttk.Label(zones_frame, text="50").pack()
        ttk.Label(zones_frame, text="70", foreground=self.colors['danger']).pack(side='right')

        # التفسير
        self.rsi_interpretation = tk.Text(rsi_frame, height=3, wrap='word')
        self.rsi_interpretation.pack(fill='x', pady=(10, 0))

    def create_macd_tab(self):
        """إنشاء تبويب مؤشر MACD"""
        macd_frame = ttk.Frame(self.indicators_notebook)
        self.indicators_notebook.add(macd_frame, text="MACD")

        # قيم MACD
        values_frame = ttk.Frame(macd_frame)
        values_frame.pack(fill='x', pady=10)

        # MACD Line
        macd_line_frame = ttk.Frame(values_frame)
        macd_line_frame.pack(fill='x')
        ttk.Label(macd_line_frame, text="MACD:").pack(side='left')
        self.macd_line_label = ttk.Label(macd_line_frame, text="--")
        self.macd_line_label.pack(side='right')

        # Signal Line
        signal_line_frame = ttk.Frame(values_frame)
        signal_line_frame.pack(fill='x')
        ttk.Label(signal_line_frame, text="Signal:").pack(side='left')
        self.macd_signal_label = ttk.Label(signal_line_frame, text="--")
        self.macd_signal_label.pack(side='right')

        # Histogram
        histogram_frame = ttk.Frame(values_frame)
        histogram_frame.pack(fill='x')
        ttk.Label(histogram_frame, text="Histogram:").pack(side='left')
        self.macd_histogram_label = ttk.Label(histogram_frame, text="--")
        self.macd_histogram_label.pack(side='right')

        # التفسير
        self.macd_interpretation = tk.Text(macd_frame, height=3, wrap='word')
        self.macd_interpretation.pack(fill='x', pady=(10, 0))

    def create_ema_tab(self):
        """إنشاء تبويب المتوسطات المتحركة"""
        ema_frame = ttk.Frame(self.indicators_notebook)
        self.indicators_notebook.add(ema_frame, text="EMA")

        # قيم EMA
        values_frame = ttk.Frame(ema_frame)
        values_frame.pack(fill='x', pady=10)

        # EMA 50
        ema50_frame = ttk.Frame(values_frame)
        ema50_frame.pack(fill='x')
        ttk.Label(ema50_frame, text="EMA 50:").pack(side='left')
        self.ema50_label = ttk.Label(ema50_frame, text="$--")
        self.ema50_label.pack(side='right')

        # EMA 200
        ema200_frame = ttk.Frame(values_frame)
        ema200_frame.pack(fill='x')
        ttk.Label(ema200_frame, text="EMA 200:").pack(side='left')
        self.ema200_label = ttk.Label(ema200_frame, text="$--")
        self.ema200_label.pack(side='right')

        # اتجاه السوق
        trend_frame = ttk.Frame(ema_frame)
        trend_frame.pack(fill='x', pady=10)
        ttk.Label(trend_frame, text="الاتجاه:").pack(side='left')
        self.trend_label = ttk.Label(trend_frame, text="--")
        self.trend_label.pack(side='right')

        # التفسير
        self.ema_interpretation = tk.Text(ema_frame, height=3, wrap='word')
        self.ema_interpretation.pack(fill='x', pady=(10, 0))

    def create_bollinger_tab(self):
        """إنشاء تبويب نطاقات بولينجر"""
        bb_frame = ttk.Frame(self.indicators_notebook)
        self.indicators_notebook.add(bb_frame, text="Bollinger")

        # قيم النطاقات
        values_frame = ttk.Frame(bb_frame)
        values_frame.pack(fill='x', pady=10)

        # النطاق العلوي
        upper_frame = ttk.Frame(values_frame)
        upper_frame.pack(fill='x')
        ttk.Label(upper_frame, text="العلوي:").pack(side='left')
        self.bb_upper_label = ttk.Label(upper_frame, text="$--")
        self.bb_upper_label.pack(side='right')

        # النطاق الأوسط
        middle_frame = ttk.Frame(values_frame)
        middle_frame.pack(fill='x')
        ttk.Label(middle_frame, text="الأوسط:").pack(side='left')
        self.bb_middle_label = ttk.Label(middle_frame, text="$--")
        self.bb_middle_label.pack(side='right')

        # النطاق السفلي
        lower_frame = ttk.Frame(values_frame)
        lower_frame.pack(fill='x')
        ttk.Label(lower_frame, text="السفلي:").pack(side='left')
        self.bb_lower_label = ttk.Label(lower_frame, text="$--")
        self.bb_lower_label.pack(side='right')

        # التفسير
        self.bb_interpretation = tk.Text(bb_frame, height=3, wrap='word')
        self.bb_interpretation.pack(fill='x', pady=(10, 0))

    def create_volume_tab(self):
        """إنشاء تبويب تحليل الحجم"""
        volume_frame = ttk.Frame(self.indicators_notebook)
        self.indicators_notebook.add(volume_frame, text="Volume")

        # قيم الحجم
        values_frame = ttk.Frame(volume_frame)
        values_frame.pack(fill='x', pady=10)

        # الحجم الحالي
        current_frame = ttk.Frame(values_frame)
        current_frame.pack(fill='x')
        ttk.Label(current_frame, text="الحجم الحالي:").pack(side='left')
        self.volume_current_label = ttk.Label(current_frame, text="--")
        self.volume_current_label.pack(side='right')

        # المتوسط
        avg_frame = ttk.Frame(values_frame)
        avg_frame.pack(fill='x')
        ttk.Label(avg_frame, text="المتوسط:").pack(side='left')
        self.volume_avg_label = ttk.Label(avg_frame, text="--")
        self.volume_avg_label.pack(side='right')

        # النسبة
        ratio_frame = ttk.Frame(values_frame)
        ratio_frame.pack(fill='x')
        ttk.Label(ratio_frame, text="النسبة:").pack(side='left')
        self.volume_ratio_label = ttk.Label(ratio_frame, text="--x")
        self.volume_ratio_label.pack(side='right')

        # شريط الحجم
        self.volume_progress = ttk.Progressbar(volume_frame, length=300, mode='determinate')
        self.volume_progress.pack(pady=10)

        # التفسير
        self.volume_interpretation = tk.Text(volume_frame, height=3, wrap='word')
        self.volume_interpretation.pack(fill='x', pady=(10, 0))

    def create_history_section(self, parent):
        """إنشاء قسم تاريخ التحليلات"""
        history_frame = ttk.LabelFrame(parent, text="📈 تاريخ التحليلات", padding=10)
        history_frame.pack(fill='both', expand=True)

        # جدول التاريخ
        columns = ('الوقت', 'التوصية', 'الثقة')
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=6)

        # تعيين عناوين الأعمدة
        self.history_tree.heading('الوقت', text='الوقت')
        self.history_tree.heading('التوصية', text='التوصية')
        self.history_tree.heading('الثقة', text='الثقة %')

        # تعيين عرض الأعمدة
        self.history_tree.column('الوقت', width=120)
        self.history_tree.column('التوصية', width=80)
        self.history_tree.column('الثقة', width=60)

        self.history_tree.pack(fill='both', expand=True)

        # شريط التمرير
        history_scrollbar = ttk.Scrollbar(history_frame, orient='vertical', command=self.history_tree.yview)
        history_scrollbar.pack(side='right', fill='y')
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)

    def create_chart_placeholder(self, parent):
        """إنشاء الرسم البياني أو مكان مؤقت"""
        chart_frame = ttk.LabelFrame(parent, text="📊 الرسم البياني", padding=10)
        chart_frame.pack(fill='both', expand=True)

        if CHART_AVAILABLE:
            # إنشاء مكون الرسم البياني
            try:
                self.chart_widget = BitcoinChartWidget(chart_frame, self.data_fetcher)
            except Exception as e:
                print(f"خطأ في إنشاء الرسم البياني: {e}")
                self.create_chart_error_message(chart_frame)
        else:
            # مكان مؤقت للرسم البياني
            placeholder_label = ttk.Label(chart_frame,
                                         text="📊 الرسم البياني غير متوفر\nيرجى تثبيت matplotlib",
                                         font=self.fonts['normal'],
                                         justify='center')
            placeholder_label.pack(expand=True)

    def create_chart_error_message(self, parent):
        """إنشاء رسالة خطأ للرسم البياني"""
        error_label = ttk.Label(parent,
                               text="❌ خطأ في تحميل الرسم البياني\nتحقق من اتصال الإنترنت",
                               font=self.fonts['normal'],
                               justify='center')
        error_label.pack(expand=True)

    def toggle_theme(self):
        """تبديل الوضع الليلي/النهاري"""
        self.is_dark_mode = not self.is_dark_mode

        if self.is_dark_mode:
            self.apply_dark_theme()
            self.theme_btn.configure(text="☀️ وضع نهاري")
            # تطبيق الثيم المظلم على الرسم البياني
            if hasattr(self, 'chart_widget'):
                self.chart_widget.set_dark_theme()
        else:
            self.apply_light_theme()
            self.theme_btn.configure(text="🌙 وضع ليلي")
            # تطبيق الثيم الفاتح على الرسم البياني
            if hasattr(self, 'chart_widget'):
                self.chart_widget.set_light_theme()

        # حفظ الإعداد
        if self.settings_manager:
            self.settings_manager.set('theme', 'dark' if self.is_dark_mode else 'light')

    def open_settings(self):
        """فتح نافذة الإعدادات"""
        if SETTINGS_AVAILABLE and self.settings_manager:
            SettingsWindow(self.root, self.settings_manager, self.on_settings_changed)

    def on_settings_changed(self):
        """معالج تغيير الإعدادات"""
        if not self.settings_manager:
            return

        # تطبيق الثيم الجديد
        new_theme = self.settings_manager.get('theme')
        if (new_theme == 'dark') != self.is_dark_mode:
            self.toggle_theme()

        # تطبيق إعدادات التحديث التلقائي
        self.auto_refresh = self.settings_manager.get('auto_refresh')

        # تطبيق إعدادات أخرى حسب الحاجة
        print("تم تطبيق الإعدادات الجديدة")

    def manual_refresh(self):
        """تحديث يدوي للبيانات"""
        self.refresh_btn.configure(state='disabled', text="🔄 جاري التحديث...")
        self.status_label.configure(text="جاري جلب البيانات...")

        # تشغيل التحديث في خيط منفصل
        threading.Thread(target=self.fetch_and_update_data, daemon=True).start()

    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        def auto_refresh_loop():
            while True:
                try:
                    if self.auto_refresh:
                        self.fetch_and_update_data()

                    # الحصول على فترة التحديث من الإعدادات
                    refresh_interval = 300  # افتراضي 5 دقائق
                    if self.settings_manager:
                        refresh_interval = self.settings_manager.get('refresh_interval', 300)

                    time.sleep(refresh_interval)
                except Exception as e:
                    print(f"خطأ في التحديث التلقائي: {e}")
                    time.sleep(60)  # إعادة المحاولة بعد دقيقة

        # بدء التحديث الأولي
        threading.Thread(target=self.fetch_and_update_data, daemon=True).start()

        # بدء التحديث التلقائي
        threading.Thread(target=auto_refresh_loop, daemon=True).start()

    def fetch_and_update_data(self):
        """جلب البيانات وتحديث الواجهة"""
        try:
            # جلب السعر الحالي
            current_price = self.data_fetcher.get_current_price()
            if current_price is None:
                self.update_status("❌ فشل في جلب السعر")
                return

            # جلب إحصائيات 24 ساعة
            stats_24h = self.data_fetcher.get_24h_stats()

            # جلب بيانات الشموع للتحليل
            df = self.data_fetcher.get_klines('1h', 200)
            if df is None or df.empty:
                self.update_status("❌ فشل في جلب بيانات التحليل")
                return

            # إجراء التحليل الفني
            analyzer = TechnicalAnalyzer(df)
            indicators = analyzer.calculate_all_indicators()

            if not indicators:
                self.update_status("❌ فشل في حساب المؤشرات")
                return

            # توليد التوصية
            signal = self.signal_generator.generate_trading_signal(indicators)

            # تحديث البيانات
            self.current_data = {
                'price': current_price,
                'stats_24h': stats_24h,
                'indicators': indicators,
                'signal': signal,
                'timestamp': datetime.now()
            }

            # تحديث الواجهة في الخيط الرئيسي
            self.root.after(0, self.update_ui)

        except Exception as e:
            print(f"خطأ في جلب البيانات: {e}")
            self.root.after(0, lambda: self.update_status(f"❌ خطأ: {str(e)}"))

    def update_ui(self):
        """تحديث عناصر الواجهة"""
        try:
            data = self.current_data

            # تحديث الأسعار
            self.update_price_section(data)

            # تحديث التوصيات
            self.update_recommendation_section(data)

            # تحديث المؤشرات
            self.update_indicators_section(data)

            # تحديث التاريخ
            self.update_history_section(data)

            # تحديث الرسم البياني
            if hasattr(self, 'chart_widget'):
                try:
                    self.chart_widget.update_chart_data()
                except Exception as e:
                    print(f"خطأ في تحديث الرسم البياني: {e}")

            # تحديث الحالة
            self.update_status("✅ تم التحديث بنجاح")
            self.refresh_btn.configure(state='normal', text="🔄 حلل الآن")

        except Exception as e:
            print(f"خطأ في تحديث الواجهة: {e}")
            self.update_status(f"❌ خطأ في التحديث: {str(e)}")

    def update_price_section(self, data):
        """تحديث قسم الأسعار"""
        price = data.get('price', 0)
        stats_24h = data.get('stats_24h', {})

        # السعر الرئيسي
        self.price_label.configure(text=f"${price:,.2f}")

        # التغيير في 24 ساعة
        if stats_24h:
            change_percent = float(stats_24h.get('priceChangePercent', 0))
            change_text = f"التغيير: {change_percent:+.2f}%"

            # تلوين حسب الاتجاه
            if change_percent > 0:
                self.change_label.configure(text=change_text, foreground=self.colors['success'])
            elif change_percent < 0:
                self.change_label.configure(text=change_text, foreground=self.colors['danger'])
            else:
                self.change_label.configure(text=change_text, foreground=self.colors['light_text'])

            # أعلى وأقل سعر
            high_price = float(stats_24h.get('highPrice', 0))
            low_price = float(stats_24h.get('lowPrice', 0))

            self.high_label.configure(text=f"أعلى: ${high_price:,.2f}")
            self.low_label.configure(text=f"أقل: ${low_price:,.2f}")

        # وقت التحديث
        timestamp = data.get('timestamp', datetime.now())
        self.update_time_label.configure(text=f"آخر تحديث: {timestamp.strftime('%H:%M:%S')}")

    def update_recommendation_section(self, data):
        """تحديث قسم التوصيات"""
        signal = data.get('signal', {})

        if not signal:
            return

        recommendation = signal.get('recommendation', 'انتظار')
        confidence = signal.get('confidence', 0)
        explanation = signal.get('explanation', 'لا يوجد تفسير متاح')
        market_sentiment = signal.get('market_sentiment', 'محايد')

        # التوصية الرئيسية
        self.recommendation_label.configure(text=recommendation)

        # تلوين التوصية
        if recommendation in ['شراء قوي', 'شراء']:
            self.recommendation_label.configure(foreground=self.colors['success'])
        elif recommendation in ['بيع قوي', 'بيع']:
            self.recommendation_label.configure(foreground=self.colors['danger'])
        else:
            self.recommendation_label.configure(foreground=self.colors['warning'])

        # مستوى الثقة
        self.confidence_label.configure(text=f"{confidence:.1f}%")
        self.confidence_progress.configure(value=confidence)

        # التفسير
        self.explanation_text.delete(1.0, tk.END)
        self.explanation_text.insert(1.0, explanation)

        # معنويات السوق
        sentiment_emoji = {"إيجابي": "😊", "سلبي": "😟", "محايد": "😐"}
        sentiment_text = f"{sentiment_emoji.get(market_sentiment, '😐')} {market_sentiment}"
        self.sentiment_label.configure(text=sentiment_text)

    def update_indicators_section(self, data):
        """تحديث قسم المؤشرات الفنية"""
        indicators = data.get('indicators', {})

        if not indicators:
            return

        # تحديث RSI
        rsi_data = indicators.get('rsi', {})
        if rsi_data:
            rsi_value = rsi_data.get('value', 0)
            self.rsi_value_label.configure(text=f"{rsi_value:.2f}")
            self.rsi_progress.configure(value=rsi_value)

            # تفسير RSI
            rsi_interpretation = rsi_data.get('interpretation', '')
            self.rsi_interpretation.delete(1.0, tk.END)
            self.rsi_interpretation.insert(1.0, rsi_interpretation)

        # تحديث MACD
        macd_data = indicators.get('macd', {})
        if macd_data:
            macd_line = macd_data.get('macd_line', 0)
            signal_line = macd_data.get('signal_line', 0)
            histogram = macd_data.get('histogram', 0)

            self.macd_line_label.configure(text=f"{macd_line:.4f}")
            self.macd_signal_label.configure(text=f"{signal_line:.4f}")
            self.macd_histogram_label.configure(text=f"{histogram:.4f}")

            # تفسير MACD
            macd_interpretation = macd_data.get('interpretation', '')
            self.macd_interpretation.delete(1.0, tk.END)
            self.macd_interpretation.insert(1.0, macd_interpretation)

        # تحديث EMA
        ema_data = indicators.get('ema', {})
        if ema_data:
            ema_50 = ema_data.get('ema_50', {}).get('value', 0)
            ema_200 = ema_data.get('ema_200', {}).get('value', 0)
            trend = ema_data.get('trend', 'محايد')

            self.ema50_label.configure(text=f"${ema_50:.2f}")
            self.ema200_label.configure(text=f"${ema_200:.2f}")
            self.trend_label.configure(text=trend)

            # تفسير EMA
            ema_interpretation = ema_data.get('interpretation', '')
            self.ema_interpretation.delete(1.0, tk.END)
            self.ema_interpretation.insert(1.0, ema_interpretation)

        # تحديث Bollinger Bands
        bb_data = indicators.get('bollinger_bands', {})
        if bb_data:
            upper = bb_data.get('upper_band', 0)
            middle = bb_data.get('middle_band', 0)
            lower = bb_data.get('lower_band', 0)

            self.bb_upper_label.configure(text=f"${upper:.2f}")
            self.bb_middle_label.configure(text=f"${middle:.2f}")
            self.bb_lower_label.configure(text=f"${lower:.2f}")

            # تفسير Bollinger
            bb_interpretation = bb_data.get('interpretation', '')
            self.bb_interpretation.delete(1.0, tk.END)
            self.bb_interpretation.insert(1.0, bb_interpretation)

        # تحديث Volume
        volume_data = indicators.get('volume', {})
        if volume_data:
            current_volume = volume_data.get('current_volume', 0)
            avg_volume = volume_data.get('avg_volume', 0)
            volume_ratio = volume_data.get('volume_ratio', 0)

            self.volume_current_label.configure(text=f"{current_volume:,.0f}")
            self.volume_avg_label.configure(text=f"{avg_volume:,.0f}")
            self.volume_ratio_label.configure(text=f"{volume_ratio:.2f}x")

            # شريط الحجم (نسبة إلى المتوسط)
            volume_percentage = min(volume_ratio * 50, 100)  # تحويل إلى نسبة مئوية
            self.volume_progress.configure(value=volume_percentage)

            # تفسير Volume
            volume_interpretation = volume_data.get('interpretation', '')
            self.volume_interpretation.delete(1.0, tk.END)
            self.volume_interpretation.insert(1.0, volume_interpretation)

    def update_history_section(self, data):
        """تحديث قسم تاريخ التحليلات"""
        signal = data.get('signal', {})
        timestamp = data.get('timestamp', datetime.now())

        if not signal:
            return

        # إضافة التحليل الجديد إلى التاريخ
        recommendation = signal.get('recommendation', 'انتظار')
        confidence = signal.get('confidence', 0)
        time_str = timestamp.strftime('%H:%M')

        # إدراج في بداية القائمة
        self.history_tree.insert('', 0, values=(time_str, recommendation, f"{confidence:.1f}"))

        # الاحتفاظ بآخر 10 تحليلات فقط
        children = self.history_tree.get_children()
        if len(children) > 10:
            for item in children[10:]:
                self.history_tree.delete(item)

    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.configure(text=message)

    def on_closing(self):
        """معالج إغلاق التطبيق"""
        self.auto_refresh = False
        self.root.quit()
        self.root.destroy()

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        # فحص المتطلبات
        print("🔍 فحص المتطلبات...")

        # اختبار الاتصال بالإنترنت
        fetcher = BitcoinDataFetcher()
        test_price = fetcher.get_current_price()

        if test_price is None:
            messagebox.showerror("خطأ في الاتصال",
                               "فشل في الاتصال بمصدر البيانات.\nيرجى التحقق من اتصال الإنترنت.")
            return

        print(f"✅ تم الاتصال بنجاح - السعر الحالي: ${test_price:,.2f}")

        # تشغيل التطبيق
        print("🚀 تشغيل محلل البتكوين الذكي...")
        app = BitcoinAnalyzerGUI()
        app.run()

    except ImportError as e:
        messagebox.showerror("خطأ في المتطلبات",
                           f"مكتبة مفقودة: {e}\nيرجى تثبيت المتطلبات أولاً.")
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
