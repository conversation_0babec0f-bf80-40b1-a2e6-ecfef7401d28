@echo off
chcp 65001 >nul
title محلل البتكوين الذكي - Bitcoin AI Analyzer

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║            🚀 محلل البتكوين الذكي 🚀                        ║
echo ║                Bitcoin AI Analyzer                           ║
echo ║                                                              ║
echo ║              تحليل فني احترافي للعملات الرقمية              ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 البحث عن ملف التشغيل...

if exist "dist\BitcoinAnalyzer.exe" (
    echo ✅ تم العثور على ملف exe
    echo 🚀 تشغيل البرنامج...
    echo.
    start "" "dist\BitcoinAnalyzer.exe"
    echo ✅ تم تشغيل البرنامج بنجاح!
    echo.
    echo 📋 ملاحظات:
    echo - سيتم فتح المتصفح تلقائياً
    echo - للخروج من البرنامج اضغط Ctrl+C في نافذة البرنامج
    echo.
) else if exist "main.py" (
    echo ⚠️ لم يتم العثور على ملف exe، تشغيل من الكود المصدري...
    echo 🔍 فحص Python...
    python --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python غير مثبت! يرجى تثبيت Python أولاً
        pause
        exit /b 1
    )
    echo ✅ Python متوفر
    echo 🚀 تشغيل البرنامج...
    python main.py
) else (
    echo ❌ لم يتم العثور على ملفات البرنامج!
    echo يرجى التأكد من وجود الملفات في المجلد الصحيح
    pause
    exit /b 1
)

echo.
echo 👋 شكراً لاستخدام محلل البتكوين الذكي!
pause
