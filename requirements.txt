# متطلبات أداة إنشاء الشهادات الرقمية
# Certificate Generator Requirements

# المكتبات المطلوبة مدمجة في Python 3.x:
# - tkinter (واجهة المستخدم الرسومية)
# - subprocess (تشغيل أوامر النظام)
# - os (عمليات نظام التشغيل)
# - tempfile (إدارة الملفات المؤقتة)
# - shutil (عمليات الملفات)
# - pathlib (التعامل مع المسارات)

# لا توجد مكتبات خارجية مطلوبة
# No external libraries required

# متطلبات النظام:
# - Python 3.6 أو أحدث
# - OpenSSL مثبت على النظام
# - نظام التشغيل Windows
