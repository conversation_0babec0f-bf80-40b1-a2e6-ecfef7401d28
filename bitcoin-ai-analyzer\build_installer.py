#!/usr/bin/env python3
"""
بناء ملف التثبيت لمحلل البتكوين الذكي
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_inno_setup():
    """فحص وجود Inno Setup"""
    inno_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe"
    ]
    
    for path in inno_paths:
        if os.path.exists(path):
            return path
    
    return None

def download_inno_setup():
    """تحميل وتثبيت Inno Setup"""
    print("🔽 Inno Setup غير مثبت. سيتم تحميله...")
    
    import webbrowser
    webbrowser.open("https://jrsoftware.org/isdl.php")
    
    print("📋 تعليمات:")
    print("1. حمل Inno Setup من الرابط الذي فُتح")
    print("2. ثبت البرنامج")
    print("3. أعد تشغيل هذا السكريبت")
    
    input("اضغط Enter بعد تثبيت Inno Setup...")
    return check_inno_setup()

def create_portable_version():
    """إنشاء نسخة محمولة"""
    print("📦 إنشاء نسخة محمولة...")
    
    portable_dir = "BitcoinAnalyzer_Portable"
    
    # إنشاء مجلد النسخة المحمولة
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    os.makedirs(portable_dir)
    
    # نسخ الملفات المطلوبة
    files_to_copy = [
        ("dist/BitcoinAnalyzer.exe", "BitcoinAnalyzer.exe"),
        ("frontend", "frontend"),
        ("backend", "backend"),
        ("data", "data"),
        ("bitcoin_icon.ico", "bitcoin_icon.ico"),
        ("دليل_المستخدم.md", "دليل_المستخدم.md"),
        ("README.md", "README.md"),
        ("LICENSE.txt", "LICENSE.txt")
    ]
    
    for src, dst in files_to_copy:
        src_path = Path(src)
        dst_path = Path(portable_dir) / dst
        
        if src_path.exists():
            if src_path.is_file():
                shutil.copy2(src_path, dst_path)
            else:
                shutil.copytree(src_path, dst_path, dirs_exist_ok=True)
            print(f"✅ نُسخ: {src}")
        else:
            print(f"⚠️ غير موجود: {src}")
    
    # إنشاء ملف تشغيل للنسخة المحمولة
    run_script = """@echo off
chcp 65001 >nul
title محلل البتكوين الذكي - Bitcoin AI Analyzer (Portable)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║            🚀 محلل البتكوين الذكي 🚀                        ║
echo ║                Bitcoin AI Analyzer                           ║
echo ║                     النسخة المحمولة                          ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل البرنامج...
start "" "BitcoinAnalyzer.exe"

echo ✅ تم تشغيل البرنامج!
echo 📋 سيتم فتح المتصفح تلقائياً
echo.
echo للخروج اضغط أي مفتاح...
pause >nul
"""
    
    with open(f"{portable_dir}/تشغيل_البرنامج.bat", "w", encoding="utf-8") as f:
        f.write(run_script)
    
    print(f"✅ تم إنشاء النسخة المحمولة في: {portable_dir}")
    return portable_dir

def build_installer():
    """بناء ملف التثبيت"""
    print("🔨 بناء ملف التثبيت...")
    
    # فحص Inno Setup
    iscc_path = check_inno_setup()
    if not iscc_path:
        iscc_path = download_inno_setup()
        if not iscc_path:
            print("❌ لم يتم العثور على Inno Setup")
            return False
    
    print(f"✅ تم العثور على Inno Setup: {iscc_path}")
    
    # إنشاء مجلد الإخراج
    os.makedirs("installer_output", exist_ok=True)
    
    # بناء ملف التثبيت
    try:
        result = subprocess.run([
            iscc_path,
            "installer.iss"
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ تم بناء ملف التثبيت بنجاح!")
            
            # البحث عن ملف التثبيت
            setup_files = list(Path("installer_output").glob("*.exe"))
            if setup_files:
                setup_file = setup_files[0]
                print(f"📦 ملف التثبيت: {setup_file}")
                return str(setup_file)
            else:
                print("⚠️ لم يتم العثور على ملف التثبيت")
                return None
        else:
            print("❌ فشل في بناء ملف التثبيت:")
            print(result.stderr)
            return None
            
    except Exception as e:
        print(f"❌ خطأ في بناء ملف التثبيت: {e}")
        return None

def create_zip_package():
    """إنشاء حزمة ZIP للنسخة المحمولة"""
    print("📦 إنشاء حزمة ZIP...")
    
    try:
        import zipfile
        
        zip_name = "BitcoinAnalyzer_Portable.zip"
        portable_dir = "BitcoinAnalyzer_Portable"
        
        if not os.path.exists(portable_dir):
            print("❌ النسخة المحمولة غير موجودة")
            return None
        
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(portable_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, portable_dir)
                    zipf.write(file_path, arc_path)
        
        print(f"✅ تم إنشاء حزمة ZIP: {zip_name}")
        return zip_name
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء حزمة ZIP: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("🚀 بناء حزم التوزيع لمحلل البتكوين الذكي")
    print("=" * 60)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = [
        "dist/BitcoinAnalyzer.exe",
        "frontend/index.html",
        "backend/app.py",
        "bitcoin_icon.ico"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nيرجى التأكد من بناء البرنامج أولاً باستخدام PyInstaller")
        return
    
    print("✅ جميع الملفات المطلوبة موجودة")
    
    # إنشاء النسخة المحمولة
    portable_dir = create_portable_version()
    
    # إنشاء حزمة ZIP
    zip_file = create_zip_package()
    
    # بناء ملف التثبيت
    setup_file = build_installer()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    
    if portable_dir and os.path.exists(portable_dir):
        print(f"✅ النسخة المحمولة: {portable_dir}/")
    
    if zip_file and os.path.exists(zip_file):
        print(f"✅ حزمة ZIP: {zip_file}")
    
    if setup_file and os.path.exists(setup_file):
        print(f"✅ ملف التثبيت: {setup_file}")
    
    print("\n🎉 تم إنجاز جميع حزم التوزيع بنجاح!")
    print("\n📋 طرق التوزيع:")
    print("1. ملف التثبيت: للمستخدمين العاديين")
    print("2. النسخة المحمولة: للاستخدام بدون تثبيت")
    print("3. حزمة ZIP: للتوزيع عبر الإنترنت")

if __name__ == "__main__":
    main()
