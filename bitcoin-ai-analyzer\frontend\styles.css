/* Bitcoin AI Analyzer - Professional Styling */

/* CSS Variables for Theme Support */
:root {
    /* Light Theme Colors */
    --primary-color: #f7931a;
    --secondary-color: #4a90e2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-card: #ffffff;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-card: #333333;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --border-color: #444444;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color), #e67e22);
    color: white;
    padding: var(--spacing-lg) 0;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo i {
    font-size: 2rem;
    color: #ffd700;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Button Styles */
.btn-primary, .btn-secondary, .btn-icon {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.btn-primary {
    background-color: var(--success-color);
    color: white;
}

.btn-primary:hover {
    background-color: #218838;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #357abd;
}

.btn-icon {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    padding: var(--spacing-sm);
    border-radius: 50%;
}

.btn-icon:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Main Content */
.main-content {
    padding: var(--spacing-xl) 0;
    min-height: calc(100vh - 200px);
}

/* Loading Indicator */
.loading-indicator {
    text-align: center;
    padding: var(--spacing-xxl);
    display: none;
}

.loading-indicator.show {
    display: block;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
    background-color: var(--danger-color);
    color: white;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Price Overview */
.price-overview {
    margin-bottom: var(--spacing-xl);
}

.price-card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.price-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.price-header h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
}

.last-update {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.price-display {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.current-price {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.price-change {
    font-size: 1.2rem;
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.price-change.positive {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.price-change.negative {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.price-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.stat {
    text-align: center;
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-md);
}

.stat .label {
    display: block;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.stat .value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Recommendation Section */
.recommendation-section {
    margin-bottom: var(--spacing-xl);
}

.recommendation-card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.confidence-meter {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.9rem;
}

.confidence-bar {
    width: 100px;
    height: 8px;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--danger-color), var(--warning-color), var(--success-color));
    transition: width var(--transition-normal);
    width: 0%;
}

.recommendation-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.recommendation-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    background-color: var(--warning-color);
    transition: all var(--transition-normal);
}

.recommendation-icon.buy {
    background-color: var(--success-color);
}

.recommendation-icon.sell {
    background-color: var(--danger-color);
}

.recommendation-text h3 {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.recommendation-text p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.market-sentiment {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-md);
}

.sentiment-emoji {
    font-size: 1.5rem;
}

/* Indicators Section */
.indicators-section {
    margin-bottom: var(--spacing-xl);
}

.indicators-section h2 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.indicators-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.indicator-card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: transform var(--transition-fast);
}

.indicator-card:hover {
    transform: translateY(-2px);
}

.indicator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.indicator-header h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
}

.indicator-value {
    font-weight: 600;
    color: var(--primary-color);
}

.indicator-interpretation {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: var(--spacing-sm);
    line-height: 1.4;
}

/* RSI Indicator Styles */
.indicator-bar {
    margin: var(--spacing-md) 0;
}

.rsi-zones {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.zone.oversold {
    color: var(--success-color);
}

.zone.overbought {
    color: var(--danger-color);
}

.indicator-progress {
    position: relative;
    height: 8px;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.indicator-fill {
    height: 100%;
    transition: width var(--transition-normal);
}

.rsi-fill {
    background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--danger-color));
}

.indicator-pointer {
    position: absolute;
    top: -2px;
    width: 4px;
    height: 12px;
    background-color: var(--text-primary);
    border-radius: 2px;
    transition: left var(--transition-normal);
}

/* MACD Indicator Styles */
.macd-values {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
}

.macd-line {
    color: var(--secondary-color);
}

.signal-line {
    color: var(--primary-color);
}

.macd-histogram {
    height: 40px;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    margin: var(--spacing-md) 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.histogram-bar {
    width: 60%;
    height: 20px;
    background-color: var(--info-color);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-normal);
}

/* EMA Indicator Styles */
.ema-values {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
}

.ema-50 {
    color: var(--secondary-color);
}

.ema-200 {
    color: var(--primary-color);
}

.trend-indicator {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: var(--spacing-md) 0;
}

.trend-arrow {
    font-size: 1.5rem;
    color: var(--text-secondary);
    transition: all var(--transition-normal);
}

.trend-arrow.bullish {
    color: var(--success-color);
    transform: rotate(-45deg);
}

.trend-arrow.bearish {
    color: var(--danger-color);
    transform: rotate(45deg);
}

/* Bollinger Bands Styles */
.bb-values {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
}

.bb-upper {
    color: var(--danger-color);
}

.bb-lower {
    color: var(--success-color);
}

.bb-position {
    margin: var(--spacing-md) 0;
}

.bb-bands {
    position: relative;
    height: 40px;
    background: linear-gradient(to bottom,
        transparent 0%,
        rgba(220, 53, 69, 0.1) 10%,
        rgba(255, 193, 7, 0.1) 50%,
        rgba(40, 167, 69, 0.1) 90%,
        transparent 100%);
    border-radius: var(--border-radius-sm);
}

.bb-band {
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: var(--border-color);
}

.bb-band.upper {
    top: 5px;
    background-color: var(--danger-color);
}

.bb-band.middle {
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--warning-color);
}

.bb-band.lower {
    bottom: 5px;
    background-color: var(--success-color);
}

.price-position {
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--transition-normal);
}

/* Volume Analysis Styles */
.volume-ratio {
    font-weight: 600;
    color: var(--info-color);
}

.volume-bar {
    height: 20px;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    margin: var(--spacing-md) 0;
    overflow: hidden;
}

.volume-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--info-color), var(--secondary-color));
    transition: width var(--transition-normal);
    width: 0%;
}

/* Chart Section */
.chart-section {
    margin-bottom: var(--spacing-xl);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.chart-header h2 {
    color: var(--text-primary);
}

.select-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-card);
    color: var(--text-primary);
    font-family: inherit;
}

.chart-container {
    background-color: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    height: 400px;
}

/* History Section */
.history-section {
    margin-bottom: var(--spacing-xl);
}

.history-section h2 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.history-container {
    background-color: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    transition: background-color var(--transition-fast);
}

.history-item:hover {
    background-color: var(--bg-secondary);
}

.history-item:last-child {
    border-bottom: none;
}

.history-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.history-recommendation {
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
}

.history-recommendation.buy {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.history-recommendation.sell {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.history-recommendation.wait {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.history-time {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.no-data {
    text-align: center;
    color: var(--text-secondary);
    padding: var(--spacing-xl);
}

/* Footer */
.footer {
    background-color: var(--bg-secondary);
    padding: var(--spacing-xl) 0;
    text-align: center;
    border-top: 1px solid var(--border-color);
    margin-top: var(--spacing-xxl);
}

.footer p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.disclaimer {
    font-size: 0.9rem;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .current-price {
        font-size: 2rem;
    }

    .price-display {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .recommendation-display {
        flex-direction: column;
        text-align: center;
    }

    .recommendation-text h3 {
        font-size: 1.5rem;
    }

    .indicators-grid {
        grid-template-columns: 1fr;
    }

    .chart-container {
        height: 300px;
    }

    .history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: var(--spacing-lg) 0;
    }

    .price-card,
    .recommendation-card,
    .indicator-card {
        padding: var(--spacing-md);
    }

    .current-price {
        font-size: 1.8rem;
    }

    .recommendation-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.hidden { display: none; }
.visible { display: block; }
