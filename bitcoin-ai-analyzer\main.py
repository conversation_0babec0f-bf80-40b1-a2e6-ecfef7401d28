#!/usr/bin/env python3
"""
Bitcoin AI Analyzer - Main Application
محلل البتكوين الذكي - التطبيق الرئيسي

هذا الملف يقوم بتشغيل النظام بالكامل ويفتح الواجهة الأمامية تلقائياً
"""

import os
import sys
import time
import threading
import webbrowser
import subprocess
from pathlib import Path

# إضافة مجلد backend إلى المسار
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

def check_dependencies():
    """فحص المتطلبات المطلوبة"""
    print("🔍 فحص المتطلبات...")

    try:
        import flask
        import pandas
        import numpy
        import ta
        import requests
        import flask_cors
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"⚠️ مكتبة مفقودة: {e}")
        print("يرجى تثبيت المتطلبات باستخدام: pip install -r requirements.txt")
        return False

def start_backend_server():
    """تشغيل خادم Flask في خيط منفصل"""
    try:
        from app import app
        print("🚀 بدء تشغيل خادم Flask...")
        app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def start_frontend_server():
    """تشغيل خادم الواجهة الأمامية"""
    try:
        frontend_path = os.path.join(os.path.dirname(__file__), 'frontend')
        os.chdir(frontend_path)
        
        print("🌐 بدء تشغيل خادم الواجهة الأمامية...")
        
        # محاولة استخدام Python HTTP server
        subprocess.Popen([
            sys.executable, '-m', 'http.server', '8000'
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        return True
    except Exception as e:
        print(f"⚠️ تعذر تشغيل خادم الواجهة: {e}")
        return False

def open_browser():
    """فتح المتصفح تلقائياً"""
    time.sleep(3)  # انتظار حتى يبدأ الخادم
    
    urls_to_try = [
        'http://localhost:8000',
        'http://127.0.0.1:8000',
        os.path.join(os.path.dirname(__file__), 'frontend', 'index.html')
    ]
    
    for url in urls_to_try:
        try:
            print(f"🌐 فتح المتصفح: {url}")
            webbrowser.open(url)
            break
        except Exception as e:
            print(f"⚠️ فشل في فتح {url}: {e}")
            continue

def run_system_test():
    """تشغيل اختبار سريع للنظام"""
    print("\n🧪 تشغيل اختبار سريع...")
    
    try:
        # اختبار جلب البيانات
        from utils import BitcoinDataFetcher
        fetcher = BitcoinDataFetcher()
        
        price = fetcher.get_current_price()
        if price:
            print(f"✅ السعر الحالي: ${price:,.2f}")
        else:
            print("⚠️ تعذر جلب السعر الحالي")
            return False
        
        # اختبار التحليل
        df = fetcher.get_klines('1h', 50)
        if df is not None and not df.empty:
            print(f"✅ تم جلب {len(df)} شمعة بنجاح")
        else:
            print("⚠️ تعذر جلب بيانات الشموع")
            return False
        
        print("✅ الاختبار السريع نجح")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاختبار السريع: {e}")
        return False

def print_banner():
    """طباعة شعار التطبيق"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║            🚀 محلل البتكوين الذكي 🚀                        ║
    ║                Bitcoin AI Analyzer                           ║
    ║                                                              ║
    ║              تحليل فني احترافي للعملات الرقمية              ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_instructions():
    """طباعة تعليمات الاستخدام"""
    instructions = """
    📋 تعليمات الاستخدام:
    
    1. سيتم فتح المتصفح تلقائياً مع الواجهة الأمامية
    2. اضغط على "حلل الآن" لبدء التحليل
    3. راجع التوصيات والمؤشرات الفنية
    4. استخدم الرسم البياني لمتابعة الأسعار
    
    🌐 الروابط:
    - الواجهة الأمامية: http://localhost:8000
    - API الخلفية: http://localhost:5000/api
    
    ⚠️ تنبيه: هذا التطبيق لأغراض تعليمية فقط وليس نصيحة استثمارية
    
    للخروج: اضغط Ctrl+C
    """
    print(instructions)

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # فحص المتطلبات
    if not check_dependencies():
        input("\nاضغط Enter للخروج...")
        return
    
    # تشغيل اختبار سريع
    if not run_system_test():
        print("⚠️ فشل الاختبار السريع. قد تواجه مشاكل في التشغيل.")
        response = input("هل تريد المتابعة؟ (y/n): ")
        if response.lower() != 'y':
            return
    
    print("\n" + "="*60)
    print("🚀 بدء تشغيل محلل البتكوين الذكي...")
    print("="*60)
    
    try:
        # تشغيل خادم الواجهة الأمامية
        frontend_started = start_frontend_server()
        
        # تشغيل خادم Flask في خيط منفصل
        backend_thread = threading.Thread(target=start_backend_server, daemon=True)
        backend_thread.start()
        
        # انتظار قصير لبدء الخوادم
        time.sleep(2)
        
        # فتح المتصفح
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # طباعة التعليمات
        print_instructions()
        
        # انتظار إدخال المستخدم للخروج
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n👋 شكراً لاستخدام محلل البتكوين الذكي!")
            print("تم إيقاف التطبيق بنجاح.")
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
