"""
Trading Signals Generator - مولد إشارات التداول
خوارزمية ذكية لتحليل المؤشرات الفنية وإعطاء توصية نهائية مع الأسباب
"""

import logging
from datetime import datetime
from typing import Dict, List, Tuple

logger = logging.getLogger(__name__)

class TradingSignalGenerator:
    """مولد إشارات التداول الذكي"""
    
    def __init__(self):
        self.signal_weights = {
            'rsi': 0.25,        # وزن مؤشر RSI
            'macd': 0.30,       # وزن مؤشر MACD
            'ema': 0.25,        # وزن المتوسطات المتحركة
            'bollinger': 0.15,  # وزن نطاقات بولينجر
            'volume': 0.05      # وزن تحليل الحجم
        }
        
        self.decision_thresholds = {
            'strong_buy': 0.7,
            'buy': 0.4,
            'neutral': 0.2,
            'sell': -0.4,
            'strong_sell': -0.7
        }
    
    def analyze_rsi_signal(self, rsi_data: Dict) -> Tuple[float, str]:
        """
        تحليل إشارة RSI
        
        Returns:
            tuple: (نقاط الإشارة، التفسير)
        """
        if not rsi_data:
            return 0, "بيانات RSI غير متوفرة"
        
        rsi_value = rsi_data.get('value', 50)
        
        if rsi_value < 25:
            return 1.0, f"RSI منخفض جداً ({rsi_value:.1f}) - تشبع بيعي قوي"
        elif rsi_value < 30:
            return 0.8, f"RSI منخفض ({rsi_value:.1f}) - تشبع بيعي"
        elif rsi_value < 40:
            return 0.3, f"RSI تحت المتوسط ({rsi_value:.1f}) - ميل للشراء"
        elif rsi_value > 75:
            return -1.0, f"RSI مرتفع جداً ({rsi_value:.1f}) - تشبع شرائي قوي"
        elif rsi_value > 70:
            return -0.8, f"RSI مرتفع ({rsi_value:.1f}) - تشبع شرائي"
        elif rsi_value > 60:
            return -0.3, f"RSI فوق المتوسط ({rsi_value:.1f}) - ميل للبيع"
        else:
            return 0, f"RSI متوازن ({rsi_value:.1f}) - منطقة محايدة"
    
    def analyze_macd_signal(self, macd_data: Dict) -> Tuple[float, str]:
        """تحليل إشارة MACD"""
        if not macd_data:
            return 0, "بيانات MACD غير متوفرة"
        
        cross_signal = macd_data.get('cross_signal', 'neutral')
        macd_value = macd_data.get('macd', 0)
        signal_value = macd_data.get('signal', 0)
        histogram = macd_data.get('histogram', 0)
        
        # تحليل التقاطع
        if cross_signal == 'bullish':
            if histogram > 0:
                return 1.0, "MACD تقاطع صاعد قوي - إشارة شراء مؤكدة"
            else:
                return 0.7, "MACD تقاطع صاعد - إشارة شراء"
        elif cross_signal == 'bearish':
            if histogram < 0:
                return -1.0, "MACD تقاطع هابط قوي - إشارة بيع مؤكدة"
            else:
                return -0.7, "MACD تقاطع هابط - إشارة بيع"
        
        # تحليل الموقع النسبي
        if macd_value > signal_value:
            strength = min(abs(histogram) / 10, 0.5)  # تطبيع القوة
            return strength, f"MACD فوق خط الإشارة - زخم صاعد"
        else:
            strength = -min(abs(histogram) / 10, 0.5)
            return strength, f"MACD تحت خط الإشارة - زخم هابط"
    
    def analyze_ema_signal(self, ema_data: Dict) -> Tuple[float, str]:
        """تحليل إشارة المتوسطات المتحركة"""
        if not ema_data:
            return 0, "بيانات EMA غير متوفرة"
        
        trend = ema_data.get('trend', 'neutral')
        current_price = ema_data.get('current_price', 0)
        ema_50 = ema_data.get('ema_50', {}).get('value', 0)
        ema_200 = ema_data.get('ema_200', {}).get('value', 0)
        
        if trend == 'bullish':
            # حساب قوة الاتجاه
            if ema_50 > ema_200:
                distance_50 = (current_price - ema_50) / ema_50 * 100
                distance_200 = (ema_50 - ema_200) / ema_200 * 100
                
                if distance_50 > 2 and distance_200 > 1:
                    return 1.0, f"اتجاه صاعد قوي - السعر فوق EMA بـ {distance_50:.1f}%"
                else:
                    return 0.7, "اتجاه صاعد - السعر فوق المتوسطات المتحركة"
            else:
                return 0.3, "بداية اتجاه صاعد محتمل"
                
        elif trend == 'bearish':
            if ema_50 < ema_200:
                distance_50 = (ema_50 - current_price) / current_price * 100
                distance_200 = (ema_200 - ema_50) / ema_50 * 100
                
                if distance_50 > 2 and distance_200 > 1:
                    return -1.0, f"اتجاه هابط قوي - السعر تحت EMA بـ {distance_50:.1f}%"
                else:
                    return -0.7, "اتجاه هابط - السعر تحت المتوسطات المتحركة"
            else:
                return -0.3, "بداية اتجاه هابط محتمل"
        
        return 0, "اتجاه مختلط - لا يوجد اتجاه واضح"
    
    def analyze_bollinger_signal(self, bollinger_data: Dict) -> Tuple[float, str]:
        """تحليل إشارة نطاقات بولينجر"""
        if not bollinger_data:
            return 0, "بيانات Bollinger غير متوفرة"
        
        position = bollinger_data.get('position', 'middle')
        width = bollinger_data.get('width', 0)
        current_price = bollinger_data.get('current_price', 0)
        upper = bollinger_data.get('upper', 0)
        lower = bollinger_data.get('lower', 0)
        
        # تحليل الموقع
        if position == 'above_upper':
            return -0.8, "السعر فوق النطاق العلوي - تشبع شرائي"
        elif position == 'below_lower':
            return 0.8, "السعر تحت النطاق السفلي - تشبع بيعي"
        elif position == 'upper_half':
            distance = (current_price - (upper + lower) / 2) / ((upper - lower) / 2)
            return -0.3 * distance, f"السعر في النصف العلوي - ضغط شرائي متوسط"
        elif position == 'lower_half':
            distance = ((upper + lower) / 2 - current_price) / ((upper - lower) / 2)
            return 0.3 * distance, f"السعر في النصف السفلي - ضغط بيعي متوسط"
        
        return 0, "السعر في منتصف النطاق - متوازن"
    
    def analyze_volume_signal(self, volume_data: Dict) -> Tuple[float, str]:
        """تحليل إشارة الحجم"""
        if not volume_data:
            return 0, "بيانات الحجم غير متوفرة"
        
        trend = volume_data.get('trend', 'weak_signal')
        ratio = volume_data.get('ratio', 1)
        
        if trend == 'bullish_confirmation':
            return 0.5, f"تأكيد صاعد بحجم عالي ({ratio:.1f}x)"
        elif trend == 'bearish_confirmation':
            return -0.5, f"تأكيد هابط بحجم عالي ({ratio:.1f}x)"
        elif ratio > 1.5:
            return 0.2, f"حجم تداول عالي ({ratio:.1f}x) - اهتمام متزايد"
        elif ratio < 0.5:
            return -0.1, f"حجم تداول منخفض ({ratio:.1f}x) - اهتمام ضعيف"
        
        return 0, "حجم تداول طبيعي"

    def generate_trading_signal(self, indicators: Dict) -> Dict:
        """
        توليد إشارة التداول النهائية

        Args:
            indicators: قاموس المؤشرات الفنية

        Returns:
            dict: التوصية النهائية مع التفسير المفصل
        """
        try:
            # تحليل كل مؤشر
            signals = {}
            explanations = {}

            # RSI
            rsi_score, rsi_explanation = self.analyze_rsi_signal(indicators.get('rsi'))
            signals['rsi'] = rsi_score
            explanations['rsi'] = rsi_explanation

            # MACD
            macd_score, macd_explanation = self.analyze_macd_signal(indicators.get('macd'))
            signals['macd'] = macd_score
            explanations['macd'] = macd_explanation

            # EMA
            ema_score, ema_explanation = self.analyze_ema_signal(indicators.get('ema'))
            signals['ema'] = ema_score
            explanations['ema'] = ema_explanation

            # Bollinger Bands
            bb_score, bb_explanation = self.analyze_bollinger_signal(indicators.get('bollinger'))
            signals['bollinger'] = bb_score
            explanations['bollinger'] = bb_explanation

            # Volume
            volume_score, volume_explanation = self.analyze_volume_signal(indicators.get('volume'))
            signals['volume'] = volume_score
            explanations['volume'] = volume_explanation

            # حساب النتيجة المرجحة
            weighted_score = 0
            for indicator, score in signals.items():
                weight = self.signal_weights.get(indicator, 0)
                weighted_score += score * weight

            # تحديد التوصية
            recommendation = self._determine_recommendation(weighted_score)

            # إنشاء التفسير المفصل
            detailed_explanation = self._create_detailed_explanation(
                recommendation, weighted_score, signals, explanations
            )

            # إنشاء ملخص الإشارات
            signal_summary = self._create_signal_summary(signals)

            result = {
                'timestamp': datetime.now().isoformat(),
                'recommendation': recommendation['action'],
                'confidence': recommendation['confidence'],
                'score': round(weighted_score, 3),
                'explanation': detailed_explanation,
                'signal_summary': signal_summary,
                'individual_signals': {
                    indicator: {
                        'score': round(score, 2),
                        'explanation': explanations[indicator]
                    }
                    for indicator, score in signals.items()
                },
                'market_sentiment': self._determine_market_sentiment(weighted_score)
            }

            logger.info(f"إشارة تداول مولدة: {recommendation['action']} (ثقة: {recommendation['confidence']:.1%})")
            return result

        except Exception as e:
            logger.error(f"خطأ في توليد إشارة التداول: {e}")
            return {
                'error': str(e),
                'recommendation': 'انتظر',
                'explanation': 'حدث خطأ في التحليل، يُنصح بالانتظار'
            }

    def _determine_recommendation(self, score: float) -> Dict:
        """تحديد التوصية بناءً على النتيجة"""
        if score >= self.decision_thresholds['strong_buy']:
            return {
                'action': 'شراء قوي',
                'confidence': min(0.95, 0.7 + (score - 0.7) * 0.5),
                'color': '#00C851',
                'icon': '🚀'
            }
        elif score >= self.decision_thresholds['buy']:
            return {
                'action': 'شراء',
                'confidence': 0.6 + (score - 0.4) * 0.3,
                'color': '#4CAF50',
                'icon': '📈'
            }
        elif score >= self.decision_thresholds['neutral']:
            return {
                'action': 'انتظر',
                'confidence': 0.5 + abs(score) * 0.2,
                'color': '#FF9800',
                'icon': '⏳'
            }
        elif score >= self.decision_thresholds['sell']:
            return {
                'action': 'بيع',
                'confidence': 0.6 + abs(score + 0.4) * 0.3,
                'color': '#F44336',
                'icon': '📉'
            }
        else:
            return {
                'action': 'بيع قوي',
                'confidence': min(0.95, 0.7 + abs(score + 0.7) * 0.5),
                'color': '#D32F2F',
                'icon': '🔻'
            }

    def _create_detailed_explanation(self, recommendation: Dict, score: float,
                                   signals: Dict, explanations: Dict) -> str:
        """إنشاء تفسير مفصل للتوصية"""
        action = recommendation['action']
        confidence = recommendation['confidence']

        explanation = f"🎯 **التوصية: {action}** (مستوى الثقة: {confidence:.1%})\n\n"

        # تفسير النتيجة الإجمالية
        if score > 0.5:
            explanation += "📊 **التحليل الإجمالي:** السوق يظهر إشارات إيجابية قوية تدعم الشراء.\n\n"
        elif score > 0:
            explanation += "📊 **التحليل الإجمالي:** السوق يظهر إشارات إيجابية خفيفة.\n\n"
        elif score > -0.5:
            explanation += "📊 **التحليل الإجمالي:** السوق في حالة تذبذب، يُنصح بالحذر.\n\n"
        else:
            explanation += "📊 **التحليل الإجمالي:** السوق يظهر إشارات سلبية تدعم البيع.\n\n"

        # تفسير المؤشرات الرئيسية
        explanation += "🔍 **تحليل المؤشرات:**\n"

        # ترتيب المؤشرات حسب القوة
        sorted_signals = sorted(signals.items(), key=lambda x: abs(x[1]), reverse=True)

        for indicator, signal_score in sorted_signals[:3]:  # أهم 3 مؤشرات
            indicator_name = {
                'rsi': 'مؤشر القوة النسبية (RSI)',
                'macd': 'مؤشر MACD',
                'ema': 'المتوسطات المتحركة (EMA)',
                'bollinger': 'نطاقات بولينجر',
                'volume': 'تحليل الحجم'
            }.get(indicator, indicator)

            explanation += f"• **{indicator_name}:** {explanations[indicator]}\n"

        explanation += f"\n💡 **الخلاصة:** {self._get_conclusion_text(action, score)}"

        return explanation

    def _create_signal_summary(self, signals: Dict) -> Dict:
        """إنشاء ملخص الإشارات"""
        positive_signals = sum(1 for score in signals.values() if score > 0.3)
        negative_signals = sum(1 for score in signals.values() if score < -0.3)
        neutral_signals = len(signals) - positive_signals - negative_signals

        return {
            'positive': positive_signals,
            'negative': negative_signals,
            'neutral': neutral_signals,
            'total': len(signals)
        }

    def _determine_market_sentiment(self, score: float) -> Dict:
        """تحديد معنويات السوق"""
        if score > 0.5:
            return {'sentiment': 'متفائل جداً', 'emoji': '🚀', 'color': '#00C851'}
        elif score > 0.2:
            return {'sentiment': 'متفائل', 'emoji': '📈', 'color': '#4CAF50'}
        elif score > -0.2:
            return {'sentiment': 'محايد', 'emoji': '😐', 'color': '#FF9800'}
        elif score > -0.5:
            return {'sentiment': 'متشائم', 'emoji': '📉', 'color': '#F44336'}
        else:
            return {'sentiment': 'متشائم جداً', 'emoji': '🔻', 'color': '#D32F2F'}

    def _get_conclusion_text(self, action: str, score: float) -> str:
        """نص الخلاصة حسب التوصية"""
        conclusions = {
            'شراء قوي': "جميع المؤشرات تشير إلى فرصة شراء ممتازة مع مخاطر منخفضة.",
            'شراء': "المؤشرات تدعم الشراء، لكن يُنصح بمراقبة السوق عن كثب.",
            'انتظر': "السوق في حالة تذبذب، من الأفضل انتظار إشارات أوضح قبل اتخاذ قرار.",
            'بيع': "المؤشرات تشير إلى ضعف في السوق، قد يكون الوقت مناسباً للبيع.",
            'بيع قوي': "إشارات قوية تدعم البيع، يُنصح بالخروج من المراكز الشرائية."
        }
        return conclusions.get(action, "يُنصح بمراجعة التحليل مع خبير مالي.")

def test_signal_generator():
    """اختبار مولد الإشارات"""
    from utils import BitcoinDataFetcher
    from analysis import TechnicalAnalyzer

    print("🔍 اختبار مولد إشارات التداول...")

    # جلب البيانات وحساب المؤشرات
    fetcher = BitcoinDataFetcher()
    df = fetcher.get_klines('1h', 200)

    if df is None or df.empty:
        print("❌ فشل في جلب البيانات")
        return

    analyzer = TechnicalAnalyzer(df)
    indicators = analyzer.calculate_all_indicators()

    if not indicators:
        print("❌ فشل في حساب المؤشرات")
        return

    # توليد الإشارة
    signal_generator = TradingSignalGenerator()
    signal = signal_generator.generate_trading_signal(indicators)

    if 'error' not in signal:
        print(f"✅ التوصية: {signal['recommendation']}")
        print(f"📊 مستوى الثقة: {signal['confidence']:.1%}")
        print(f"🎯 النتيجة: {signal['score']}")
        print(f"💭 المعنويات: {signal['market_sentiment']['sentiment']}")
    else:
        print(f"❌ خطأ: {signal['error']}")

if __name__ == "__main__":
    test_signal_generator()
