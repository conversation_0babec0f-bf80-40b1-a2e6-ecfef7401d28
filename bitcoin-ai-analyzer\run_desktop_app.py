#!/usr/bin/env python3
"""
Bitcoin AI Analyzer - Desktop Application Launcher
محلل البتكوين الذكي - مشغل تطبيق سطح المكتب

ملف تشغيل مخصص للتطبيق المكتبي مع فحص المتطلبات
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox
import importlib.util

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        messagebox.showerror("خطأ في الإصدار", 
                           f"يتطلب Python 3.8 أو أحدث\nالإصدار الحالي: {sys.version}")
        return False
    return True

def check_required_modules():
    """فحص المكتبات المطلوبة"""
    required_modules = [
        ('tkinter', 'tkinter'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('requests', 'requests'),
        ('ta', 'ta'),
        ('matplotlib', 'matplotlib')
    ]
    
    missing_modules = []
    
    for module_name, import_name in required_modules:
        try:
            spec = importlib.util.find_spec(import_name)
            if spec is None:
                missing_modules.append(module_name)
        except ImportError:
            missing_modules.append(module_name)
    
    return missing_modules

def install_missing_modules(missing_modules):
    """تثبيت المكتبات المفقودة"""
    if not missing_modules:
        return True
    
    result = messagebox.askyesno("مكتبات مفقودة", 
                                f"المكتبات التالية مفقودة:\n{', '.join(missing_modules)}\n\nهل تريد تثبيتها تلقائياً؟")
    
    if not result:
        return False
    
    try:
        # إنشاء نافذة تقدم
        progress_window = tk.Toplevel()
        progress_window.title("تثبيت المكتبات")
        progress_window.geometry("400x200")
        progress_window.resizable(False, False)
        
        progress_label = tk.Label(progress_window, text="جاري تثبيت المكتبات...", 
                                 font=('Arial', 12))
        progress_label.pack(pady=20)
        
        status_label = tk.Label(progress_window, text="", font=('Arial', 10))
        status_label.pack()
        
        progress_window.update()
        
        # تثبيت المكتبات
        for module in missing_modules:
            status_label.config(text=f"تثبيت {module}...")
            progress_window.update()
            
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', module], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                progress_window.destroy()
                messagebox.showerror("خطأ في التثبيت", 
                                   f"فشل في تثبيت {module}:\n{result.stderr}")
                return False
        
        progress_window.destroy()
        messagebox.showinfo("تم التثبيت", "تم تثبيت جميع المكتبات بنجاح!")
        return True
        
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ أثناء التثبيت: {e}")
        return False

def check_internet_connection():
    """فحص الاتصال بالإنترنت"""
    try:
        import requests
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=5)
        return response.status_code == 200
    except:
        return False

def show_startup_splash():
    """عرض شاشة البداية"""
    splash = tk.Tk()
    splash.title("محلل البتكوين الذكي")
    splash.geometry("500x300")
    splash.resizable(False, False)
    
    # إخفاء شريط العنوان
    splash.overrideredirect(True)
    
    # توسيط النافذة
    splash.update_idletasks()
    x = (splash.winfo_screenwidth() // 2) - (500 // 2)
    y = (splash.winfo_screenheight() // 2) - (300 // 2)
    splash.geometry(f"500x300+{x}+{y}")
    
    # خلفية ملونة
    splash.configure(bg='#F7931A')
    
    # العنوان
    title_label = tk.Label(splash, text="🚀 محلل البتكوين الذكي", 
                          font=('Arial', 20, 'bold'),
                          bg='#F7931A', fg='white')
    title_label.pack(pady=30)
    
    subtitle_label = tk.Label(splash, text="Bitcoin AI Analyzer", 
                             font=('Arial', 14),
                             bg='#F7931A', fg='white')
    subtitle_label.pack()
    
    # رسالة التحميل
    loading_label = tk.Label(splash, text="جاري التحميل...", 
                            font=('Arial', 12),
                            bg='#F7931A', fg='white')
    loading_label.pack(pady=20)
    
    # معلومات إضافية
    info_label = tk.Label(splash, text="تحليل فني احترافي للبتكوين\nمع توصيات ذكية ورسوم بيانية تفاعلية", 
                         font=('Arial', 10),
                         bg='#F7931A', fg='white',
                         justify='center')
    info_label.pack(pady=10)
    
    # تحديث النافذة
    splash.update()
    
    return splash, loading_label

def main():
    """الدالة الرئيسية"""
    # إنشاء نافذة مؤقتة لفحص المتطلبات
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    try:
        # عرض شاشة البداية
        splash, loading_label = show_startup_splash()
        
        # فحص إصدار Python
        loading_label.config(text="فحص إصدار Python...")
        splash.update()
        
        if not check_python_version():
            splash.destroy()
            return
        
        # فحص المكتبات المطلوبة
        loading_label.config(text="فحص المكتبات المطلوبة...")
        splash.update()
        
        missing_modules = check_required_modules()
        
        if missing_modules:
            splash.destroy()
            if not install_missing_modules(missing_modules):
                return
            
            # إعادة فحص بعد التثبيت
            missing_modules = check_required_modules()
            if missing_modules:
                messagebox.showerror("خطأ", f"فشل في تثبيت: {', '.join(missing_modules)}")
                return
        
        # فحص الاتصال بالإنترنت
        loading_label.config(text="فحص الاتصال بالإنترنت...")
        splash.update()
        
        if not check_internet_connection():
            splash.destroy()
            result = messagebox.askyesno("تحذير", 
                                       "لا يوجد اتصال بالإنترنت أو مشكلة في الوصول لمصدر البيانات.\n\nهل تريد المتابعة؟ (قد لا تعمل جميع الميزات)")
            if not result:
                return
        
        # تشغيل التطبيق
        loading_label.config(text="تشغيل التطبيق...")
        splash.update()
        
        # إضافة مجلد backend إلى المسار
        backend_path = os.path.join(os.path.dirname(__file__), 'backend')
        if backend_path not in sys.path:
            sys.path.insert(0, backend_path)
        
        # استيراد وتشغيل التطبيق
        from desktop_app import BitcoinAnalyzerGUI
        
        splash.destroy()
        root.destroy()
        
        # تشغيل التطبيق الرئيسي
        app = BitcoinAnalyzerGUI()
        app.run()
        
    except ImportError as e:
        if 'splash' in locals():
            splash.destroy()
        messagebox.showerror("خطأ في الاستيراد", 
                           f"فشل في استيراد مكتبة مطلوبة:\n{e}\n\nيرجى تثبيت المتطلبات باستخدام:\npip install -r requirements_desktop.txt")
    except Exception as e:
        if 'splash' in locals():
            splash.destroy()
        messagebox.showerror("خطأ", f"حدث خطأ غير متوقع:\n{e}")
    finally:
        if root.winfo_exists():
            root.destroy()

if __name__ == "__main__":
    main()
