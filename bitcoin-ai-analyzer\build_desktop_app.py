#!/usr/bin/env python3
"""
Build Desktop Application
بناء تطبيق سطح المكتب لمحلل البتكوين الذكي
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    """فحص المتطلبات المطلوبة"""
    print("🔍 فحص المتطلبات...")
    
    required_modules = [
        'pyinstaller',
        'tkinter',
        'pandas',
        'numpy',
        'requests',
        'ta',
        'matplotlib'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'tkinter':
                import tkinter
            else:
                __import__(module)
            print(f"✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module}")
    
    if missing_modules:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing_modules)}")
        print("يرجى تثبيتها باستخدام:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def create_spec_file():
    """إنشاء ملف spec لـ PyInstaller"""
    print("📝 إنشاء ملف spec...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# البيانات الإضافية المطلوبة
added_files = [
    ('backend/*.py', 'backend'),
    ('bitcoin_icon.ico', '.'),
    ('requirements_desktop.txt', '.'),
]

# المكتبات المخفية
hidden_imports = [
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.font',
    'pandas',
    'numpy',
    'requests',
    'ta',
    'matplotlib',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.figure',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'sqlite3',
    'json',
    'threading',
    'datetime',
    'time',
    'os',
    'sys'
]

a = Analysis(
    ['run_desktop_app.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'flask',
        'flask_cors',
        'werkzeug',
        'jinja2',
        'click',
        'itsdangerous',
        'markupsafe'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='BitcoinAnalyzer_Desktop',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # نافذة GUI فقط
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='bitcoin_icon.ico',
    version_file=None,
)
'''
    
    with open('desktop_app.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف spec")

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    
    try:
        # تشغيل PyInstaller
        result = subprocess.run([
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'desktop_app.spec'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ فشل في بناء الملف التنفيذي:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بناء الملف التنفيذي: {e}")
        return False

def create_installer():
    """إنشاء مثبت للتطبيق المكتبي"""
    print("📦 إنشاء مثبت التطبيق...")
    
    # إنشاء مجلد التوزيع
    dist_folder = "BitcoinAnalyzer_Desktop_Distribution"
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    os.makedirs(dist_folder)
    
    # نسخ الملف التنفيذي
    exe_source = "dist/BitcoinAnalyzer_Desktop.exe"
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, dist_folder)
        print(f"✅ نُسخ: {exe_source}")
    else:
        print(f"❌ لم يتم العثور على: {exe_source}")
        return False
    
    # نسخ الملفات الإضافية
    additional_files = [
        "bitcoin_icon.ico",
        "دليل_المستخدم.md",
        "README.md",
        "LICENSE.txt"
    ]
    
    for file in additional_files:
        if os.path.exists(file):
            shutil.copy2(file, dist_folder)
            print(f"✅ نُسخ: {file}")
    
    # إنشاء ملف تشغيل
    run_script = """@echo off
chcp 65001 >nul
title محلل البتكوين الذكي - Bitcoin AI Analyzer

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║            🚀 محلل البتكوين الذكي 🚀                        ║
echo ║                Bitcoin AI Analyzer                           ║
echo ║                   تطبيق سطح المكتب                          ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل التطبيق...
start "" "BitcoinAnalyzer_Desktop.exe"

echo ✅ تم تشغيل التطبيق!
echo 📋 إذا لم يظهر التطبيق، تحقق من:
echo    - اتصال الإنترنت
echo    - إعدادات مكافح الفيروسات
echo.
"""
    
    with open(f"{dist_folder}/تشغيل_التطبيق.bat", "w", encoding="utf-8") as f:
        f.write(run_script)
    
    # إنشاء ملف معلومات
    info_content = """محلل البتكوين الذكي - تطبيق سطح المكتب
=======================================

🚀 مرحباً بك في محلل البتكوين الذكي!

📋 كيفية التشغيل:
1. انقر نقراً مزدوجاً على "تشغيل_التطبيق.bat"
2. أو انقر مباشرة على "BitcoinAnalyzer_Desktop.exe"

📊 المميزات:
✅ تحليل فني شامل للبتكوين
✅ توصيات ذكية مع الأسباب
✅ رسوم بيانية تفاعلية
✅ واجهة سطح مكتب مستقلة
✅ وضع ليلي/نهاري
✅ إعدادات قابلة للتخصيص

⚠️ تنبيه مهم:
هذا البرنامج لأغراض تعليمية فقط وليس نصيحة استثمارية.

📞 للدعم:
راجع ملف "دليل_المستخدم.md" للمزيد من المعلومات.

---
تم تطوير هذا البرنامج بـ ❤️ لمجتمع التحليل الفني
"""
    
    with open(f"{dist_folder}/اقرأني.txt", "w", encoding="utf-8") as f:
        f.write(info_content)
    
    print(f"✅ تم إنشاء مجلد التوزيع: {dist_folder}")
    return True

def create_zip_package():
    """إنشاء حزمة ZIP للتوزيع"""
    print("📦 إنشاء حزمة ZIP...")
    
    try:
        import zipfile
        
        zip_name = "BitcoinAnalyzer_Desktop.zip"
        dist_folder = "BitcoinAnalyzer_Desktop_Distribution"
        
        if not os.path.exists(dist_folder):
            print("❌ مجلد التوزيع غير موجود")
            return False
        
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(dist_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, dist_folder)
                    zipf.write(file_path, arc_path)
        
        print(f"✅ تم إنشاء حزمة ZIP: {zip_name}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء حزمة ZIP: {e}")
        return False

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    temp_folders = ['build', '__pycache__']
    temp_files = ['desktop_app.spec']
    
    for folder in temp_folders:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"🗑️ حُذف: {folder}")
    
    for file in temp_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ حُذف: {file}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بناء تطبيق سطح المكتب لمحلل البتكوين الذكي")
    print("=" * 60)
    
    # فحص المتطلبات
    if not check_requirements():
        return
    
    # إنشاء ملف spec
    create_spec_file()
    
    # بناء الملف التنفيذي
    if not build_executable():
        return
    
    # إنشاء مثبت
    if not create_installer():
        return
    
    # إنشاء حزمة ZIP
    create_zip_package()
    
    # تنظيف
    cleanup()
    
    print("\n" + "=" * 60)
    print("🎉 تم إنجاز بناء التطبيق بنجاح!")
    print("\n📦 الملفات الجاهزة:")
    print("1. BitcoinAnalyzer_Desktop_Distribution/ - مجلد التوزيع")
    print("2. BitcoinAnalyzer_Desktop.zip - حزمة ZIP للتوزيع")
    print("3. dist/BitcoinAnalyzer_Desktop.exe - الملف التنفيذي")
    
    print("\n🚀 طرق التشغيل:")
    print("1. من مجلد التوزيع: انقر على تشغيل_التطبيق.bat")
    print("2. مباشرة: انقر على BitcoinAnalyzer_Desktop.exe")
    print("3. للتوزيع: استخدم BitcoinAnalyzer_Desktop.zip")

if __name__ == "__main__":
    main()
