@echo off
chcp 65001 >nul
title محلل البتكوين الذكي - تطبيق سطح المكتب

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║            🚀 محلل البتكوين الذكي 🚀                        ║
echo ║                Bitcoin AI Analyzer                           ║
echo ║                   تطبيق سطح المكتب                          ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

echo.
echo 🚀 تشغيل محلل البتكوين الذكي...
echo 📋 سيتم فتح نافذة التطبيق قريباً...
echo.

python run_desktop_app.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. تأكد من اتصال الإنترنت
    echo 2. ثبت المتطلبات: pip install -r requirements_desktop.txt
    echo 3. أعد تشغيل الملف كمسؤول
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق التطبيق بنجاح
pause
