# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# مسار المشروع
project_path = Path(__file__).parent

block_cipher = None

# البيانات الإضافية (الواجهة الأمامية)
added_files = [
    (str(project_path / 'frontend'), 'frontend'),
    (str(project_path / 'backend'), 'backend'),
    (str(project_path / 'data'), 'data'),
]

# المكتبات المخفية
hiddenimports = [
    'flask',
    'flask_cors',
    'pandas',
    'numpy',
    'ta',
    'ta.trend',
    'ta.momentum', 
    'ta.volatility',
    'ta.volume',
    'requests',
    'sqlite3',
    'threading',
    'webbrowser',
    'subprocess',
    'json',
    'datetime',
    'logging',
]

a = Analysis(
    ['main.py'],
    pathex=[str(project_path)],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='BitcoinAnalyzer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
