# محلل البتكوين الذكي | Bitcoin AI Analyzer

## 🚀 نظرة عامة

محلل البتكوين الذكي هو تطبيق ويب احترافي يقوم بتحليل سوق البتكوين باستخدام المؤشرات الفنية المتقدمة ويقدم توصيات ذكية للتداول. يستخدم التطبيق خوارزميات التحليل الفني لتقييم حالة السوق وإعطاء توصيات مدروسة (شراء/بيع/انتظار) مع شرح مفصل للأسباب.

## ✨ المميزات الرئيسية

### 📊 التحليل الفني الشامل
- **مؤشر القوة النسبية (RSI)**: تحديد حالات التشبع البيعي والشرائي
- **مؤشر MACD**: تحليل الزخم واتجاه السوق
- **المتوسطات المتحركة (EMA 50/200)**: تحديد الاتجاه العام
- **نطاقات بولينجر**: قياس التقلبات والدعم والمقاومة
- **تحليل الحجم**: تأكيد الاتجاهات والانعكاسات

### 🎯 نظام التوصيات الذكي
- توصيات واضحة: **شراء قوي** | **شراء** | **انتظار** | **بيع** | **بيع قوي**
- مستوى ثقة مئوي لكل توصية
- تفسير مفصل بلغة مبسطة للأسباب
- تحليل معنويات السوق

### 📈 واجهة مستخدم احترافية
- تصميم متجاوب يعمل على جميع الأجهزة
- وضع ليلي/نهاري قابل للتبديل
- رسوم بيانية تفاعلية للأسعار
- عرض المؤشرات الفنية بصرياً
- تاريخ التحليلات السابقة

### 🔄 التحديث المستمر
- بيانات لحظية من Binance API
- تحديث تلقائي كل 5 دقائق
- إمكانية التحليل الفوري بضغطة زر
- حفظ التحليلات في قاعدة بيانات محلية

## 🛠️ التقنيات المستخدمة

### الخلفية (Backend)
- **Python 3.10+**
- **Flask**: إطار عمل الويب
- **pandas & numpy**: معالجة البيانات
- **ta**: حساب المؤشرات الفنية
- **requests**: جلب البيانات من APIs
- **SQLite**: قاعدة البيانات المحلية

### الواجهة الأمامية (Frontend)
- **HTML5 & CSS3**: هيكل وتصميم الصفحة
- **JavaScript ES6+**: منطق التطبيق
- **Chart.js**: الرسوم البيانية التفاعلية
- **Font Awesome**: الأيقونات
- **Google Fonts**: خطوط عربية احترافية

## 📋 متطلبات التشغيل

- **Python 3.10** أو أحدث
- **pip** لإدارة الحزم
- اتصال بالإنترنت لجلب البيانات
- متصفح ويب حديث

## 🚀 تعليمات التثبيت والتشغيل

### 1. تحميل المشروع
```bash
# استنساخ المشروع أو تحميل الملفات
cd bitcoin-ai-analyzer
```

### 2. إنشاء بيئة افتراضية (مُستحسن)
```bash
# إنشاء بيئة افتراضية
python -m venv bitcoin_env

# تفعيل البيئة الافتراضية
# على Windows:
bitcoin_env\Scripts\activate
# على macOS/Linux:
source bitcoin_env/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تشغيل الخادم
```bash
cd backend
python app.py
```

سيبدأ الخادم على العنوان: `http://localhost:5000`

### 5. فتح الواجهة الأمامية
افتح ملف `frontend/index.html` في متصفحك أو استخدم خادم محلي:

```bash
# باستخدام Python
cd frontend
python -m http.server 8000
```

ثم افتح: `http://localhost:8000`

## 📁 هيكل المشروع

```
bitcoin-ai-analyzer/
│
├── frontend/                 # الواجهة الأمامية
│   ├── index.html           # الصفحة الرئيسية
│   ├── styles.css           # ملف التصميم
│   └── script.js            # منطق JavaScript
│
├── backend/                  # الخلفية
│   ├── app.py               # خادم Flask الرئيسي
│   ├── utils.py             # جلب البيانات من APIs
│   ├── analysis.py          # حساب المؤشرات الفنية
│   └── signals.py           # خوارزمية اتخاذ القرار
│
├── data/                     # قاعدة البيانات
│   └── bitcoin_analysis.db  # ملف SQLite (يُنشأ تلقائياً)
│
├── requirements.txt          # متطلبات Python
└── README.md                # هذا الملف
```

## 🔧 APIs المتاحة

### جلب السعر الحالي
```
GET /api/current-price
```

### تحليل السوق
```
GET /api/analyze?timeframe=1h&limit=200
```

### جلب المؤشرات الفنية
```
GET /api/indicators?timeframe=1h
```

### بيانات الرسم البياني
```
GET /api/chart-data?timeframe=1h&limit=100
```

### تاريخ التحليلات
```
GET /api/history?limit=10
```

### نظرة عامة على السوق
```
GET /api/market-overview
```

## ⚙️ إعدادات التخصيص

### تغيير مصدر البيانات
يمكنك تعديل ملف `backend/utils.py` لتغيير مصدر البيانات أو إضافة مصادر جديدة.

### تخصيص المؤشرات الفنية
في ملف `backend/analysis.py` يمكنك:
- تعديل فترات المؤشرات
- إضافة مؤشرات جديدة
- تخصيص حسابات المؤشرات

### تخصيص خوارزمية القرار
في ملف `backend/signals.py` يمكنك:
- تعديل أوزان المؤشرات
- تغيير عتبات القرار
- إضافة منطق تحليل جديد

## 🎨 تخصيص الواجهة

### الألوان والثيمات
عدّل متغيرات CSS في `frontend/styles.css`:
```css
:root {
    --primary-color: #f7931a;    /* لون البتكوين */
    --success-color: #28a745;    /* لون الشراء */
    --danger-color: #dc3545;     /* لون البيع */
    /* ... المزيد من الألوان */
}
```

### إضافة لغات جديدة
يمكن إضافة دعم لغات أخرى بتعديل النصوص في ملفات HTML و JavaScript.

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ في الاتصال بـ API**
```
خطأ: فشل في جلب بيانات السوق
الحل: تأكد من اتصال الإنترنت وأن Binance API متاح
```

**2. خطأ في تثبيت المكتبات**
```
خطأ: pip install فشل
الحل: تأكد من تحديث pip: python -m pip install --upgrade pip
```

**3. خطأ في قاعدة البيانات**
```
خطأ: لا يمكن إنشاء قاعدة البيانات
الحل: تأكد من صلاحيات الكتابة في مجلد data/
```

### تفعيل وضع التطوير
```bash
# تشغيل مع تفعيل وضع Debug
export FLASK_ENV=development  # Linux/Mac
set FLASK_ENV=development     # Windows
python app.py
```

## 📊 أمثلة على الاستخدام

### تحليل سريع
1. افتح التطبيق في المتصفح
2. اضغط على "حلل الآن"
3. انتظر النتائج (عادة 10-30 ثانية)
4. اقرأ التوصية والتفسير

### مراقبة مستمرة
- اترك التطبيق مفتوحاً للتحديث التلقائي
- راجع تاريخ التحليلات لمتابعة الاتجاهات
- استخدم الرسم البياني لفهم حركة السعر

## ⚠️ تنبيهات مهمة

1. **هذا التطبيق لأغراض تعليمية وتحليلية فقط**
2. **ليس نصيحة استثمارية أو مالية**
3. **استشر خبير مالي قبل اتخاذ قرارات استثمارية**
4. **الأسواق المالية محفوفة بالمخاطر**
5. **لا تستثمر أكثر مما يمكنك تحمل خسارته**

## 🤝 المساهمة في المشروع

نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الكود
- إضافة مؤشرات فنية جديدة
- تحسين الواجهة

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك أسئلة، يمكنك:
- مراجعة قسم استكشاف الأخطاء
- فحص ملفات السجلات (logs)
- التأكد من متطلبات النظام

---

**تم تطوير هذا المشروع بـ ❤️ لمجتمع التحليل الفني والعملات الرقمية**
