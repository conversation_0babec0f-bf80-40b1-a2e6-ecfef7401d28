#!/usr/bin/env python3
"""
إنشاء ملف تثبيت بسيط لمحلل البتكوين الذكي
"""

import os
import sys
import shutil
import winreg
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, filedialog
import threading
import subprocess

class BitcoinAnalyzerInstaller:
    def __init__(self):
        self.app_name = "Bitcoin AI Analyzer"
        self.app_version = "1.0"
        self.app_publisher = "Bitcoin AI Team"
        self.default_install_path = os.path.join(os.environ.get('PROGRAMFILES', 'C:\\Program Files'), 'Bitcoin AI Analyzer')
        
        self.root = tk.Tk()
        self.root.title(f"تثبيت {self.app_name}")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # متغيرات
        self.install_path = tk.StringVar(value=self.default_install_path)
        self.create_desktop_shortcut = tk.BooleanVar(value=True)
        self.create_start_menu = tk.BooleanVar(value=True)
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self.root, bg='#F7931A', height=80)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="محلل البتكوين الذكي", 
                              font=('Arial', 18, 'bold'), 
                              bg='#F7931A', fg='white')
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(title_frame, text="Bitcoin AI Analyzer - تحليل فني احترافي", 
                                 font=('Arial', 10), 
                                 bg='#F7931A', fg='white')
        subtitle_label.pack()
        
        # المحتوى الرئيسي
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # معلومات البرنامج
        info_text = """
مرحباً بك في معالج تثبيت محلل البتكوين الذكي!

هذا البرنامج يوفر:
• تحليل فني شامل للبتكوين
• توصيات ذكية (شراء/بيع/انتظار)
• واجهة مستخدم احترافية
• رسوم بيانية تفاعلية
• تحديث تلقائي للبيانات

⚠️ تنبيه: هذا البرنامج لأغراض تعليمية فقط وليس نصيحة استثمارية.
        """
        
        info_label = tk.Label(main_frame, text=info_text, 
                             font=('Arial', 9), justify='right')
        info_label.pack(pady=(0, 20))
        
        # مسار التثبيت
        path_frame = tk.Frame(main_frame)
        path_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(path_frame, text="مسار التثبيت:", font=('Arial', 10, 'bold')).pack(anchor='e')
        
        path_entry_frame = tk.Frame(path_frame)
        path_entry_frame.pack(fill='x', pady=(5, 0))
        
        path_entry = tk.Entry(path_entry_frame, textvariable=self.install_path, 
                             font=('Arial', 9), width=50)
        path_entry.pack(side='right', fill='x', expand=True, padx=(0, 5))
        
        browse_btn = tk.Button(path_entry_frame, text="تصفح...", 
                              command=self.browse_install_path)
        browse_btn.pack(side='right')
        
        # خيارات التثبيت
        options_frame = tk.Frame(main_frame)
        options_frame.pack(fill='x', pady=(20, 0))
        
        tk.Label(options_frame, text="خيارات التثبيت:", font=('Arial', 10, 'bold')).pack(anchor='e')
        
        desktop_check = tk.Checkbutton(options_frame, text="إنشاء اختصار على سطح المكتب", 
                                      variable=self.create_desktop_shortcut,
                                      font=('Arial', 9))
        desktop_check.pack(anchor='e', pady=(5, 0))
        
        start_menu_check = tk.Checkbutton(options_frame, text="إضافة إلى قائمة ابدأ", 
                                         variable=self.create_start_menu,
                                         font=('Arial', 9))
        start_menu_check.pack(anchor='e', pady=(2, 0))
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(side='bottom', fill='x', pady=(20, 0))
        
        cancel_btn = tk.Button(buttons_frame, text="إلغاء", 
                              command=self.cancel_install,
                              font=('Arial', 10))
        cancel_btn.pack(side='left')
        
        install_btn = tk.Button(buttons_frame, text="تثبيت", 
                               command=self.start_install,
                               font=('Arial', 10, 'bold'),
                               bg='#4CAF50', fg='white')
        install_btn.pack(side='right')
    
    def browse_install_path(self):
        """تصفح مسار التثبيت"""
        path = filedialog.askdirectory(initialdir=os.path.dirname(self.install_path.get()))
        if path:
            self.install_path.set(os.path.join(path, 'Bitcoin AI Analyzer'))
    
    def cancel_install(self):
        """إلغاء التثبيت"""
        self.root.quit()
    
    def start_install(self):
        """بدء عملية التثبيت"""
        install_path = self.install_path.get()
        
        if not install_path:
            messagebox.showerror("خطأ", "يرجى تحديد مسار التثبيت")
            return
        
        # تأكيد التثبيت
        result = messagebox.askyesno("تأكيد التثبيت", 
                                    f"سيتم تثبيت البرنامج في:\n{install_path}\n\nهل تريد المتابعة؟")
        if not result:
            return
        
        # بدء التثبيت في خيط منفصل
        threading.Thread(target=self.install_program, daemon=True).start()
    
    def install_program(self):
        """تثبيت البرنامج"""
        try:
            install_path = self.install_path.get()
            
            # إنشاء نافذة التقدم
            progress_window = tk.Toplevel(self.root)
            progress_window.title("جاري التثبيت...")
            progress_window.geometry("400x150")
            progress_window.resizable(False, False)
            progress_window.grab_set()
            
            progress_label = tk.Label(progress_window, text="جاري التثبيت...", 
                                     font=('Arial', 12))
            progress_label.pack(pady=20)
            
            status_label = tk.Label(progress_window, text="", font=('Arial', 9))
            status_label.pack()
            
            def update_status(text):
                status_label.config(text=text)
                progress_window.update()
            
            # إنشاء مجلد التثبيت
            update_status("إنشاء مجلد التثبيت...")
            os.makedirs(install_path, exist_ok=True)
            
            # نسخ الملفات
            files_to_copy = [
                ("dist/BitcoinAnalyzer.exe", "BitcoinAnalyzer.exe"),
                ("frontend", "frontend"),
                ("backend", "backend"),
                ("data", "data"),
                ("bitcoin_icon.ico", "bitcoin_icon.ico"),
                ("دليل_المستخدم.md", "دليل_المستخدم.md"),
                ("README.md", "README.md"),
                ("LICENSE.txt", "LICENSE.txt")
            ]
            
            total_files = len(files_to_copy)
            for i, (src, dst) in enumerate(files_to_copy):
                update_status(f"نسخ الملفات... ({i+1}/{total_files})")
                
                src_path = Path(src)
                dst_path = Path(install_path) / dst
                
                if src_path.exists():
                    if src_path.is_file():
                        shutil.copy2(src_path, dst_path)
                    else:
                        if dst_path.exists():
                            shutil.rmtree(dst_path)
                        shutil.copytree(src_path, dst_path)
            
            # إنشاء الاختصارات
            if self.create_desktop_shortcut.get():
                update_status("إنشاء اختصار سطح المكتب...")
                self.create_desktop_shortcut_file(install_path)
            
            if self.create_start_menu.get():
                update_status("إضافة إلى قائمة ابدأ...")
                self.create_start_menu_shortcut(install_path)
            
            # تسجيل البرنامج في النظام
            update_status("تسجيل البرنامج...")
            self.register_program(install_path)
            
            progress_window.destroy()
            
            # رسالة النجاح
            result = messagebox.askyesno("تم التثبيت بنجاح!", 
                                        "تم تثبيت محلل البتكوين الذكي بنجاح!\n\nهل تريد تشغيل البرنامج الآن؟")
            
            if result:
                subprocess.Popen([os.path.join(install_path, "BitcoinAnalyzer.exe")])
            
            self.root.quit()
            
        except Exception as e:
            messagebox.showerror("خطأ في التثبيت", f"حدث خطأ أثناء التثبيت:\n{str(e)}")
    
    def create_desktop_shortcut_file(self, install_path):
        """إنشاء اختصار على سطح المكتب"""
        try:
            import win32com.client
            
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            shortcut_path = os.path.join(desktop, "Bitcoin AI Analyzer.lnk")
            
            shell = win32com.client.Dispatch("WScript.Shell")
            shortcut = shell.CreateShortCut(shortcut_path)
            shortcut.Targetpath = os.path.join(install_path, "BitcoinAnalyzer.exe")
            shortcut.WorkingDirectory = install_path
            shortcut.IconLocation = os.path.join(install_path, "bitcoin_icon.ico")
            shortcut.Description = "محلل البتكوين الذكي - تحليل فني احترافي"
            shortcut.save()
            
        except ImportError:
            # إنشاء ملف bat بديل
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            bat_path = os.path.join(desktop, "Bitcoin AI Analyzer.bat")
            
            with open(bat_path, "w", encoding="utf-8") as f:
                f.write(f'@echo off\ncd /d "{install_path}"\nstart "" "BitcoinAnalyzer.exe"\n')
    
    def create_start_menu_shortcut(self, install_path):
        """إنشاء اختصار في قائمة ابدأ"""
        try:
            start_menu = os.path.join(os.environ['APPDATA'], 
                                     "Microsoft", "Windows", "Start Menu", "Programs")
            app_folder = os.path.join(start_menu, "Bitcoin AI Analyzer")
            os.makedirs(app_folder, exist_ok=True)
            
            # إنشاء ملف bat
            bat_path = os.path.join(app_folder, "Bitcoin AI Analyzer.bat")
            with open(bat_path, "w", encoding="utf-8") as f:
                f.write(f'@echo off\ncd /d "{install_path}"\nstart "" "BitcoinAnalyzer.exe"\n')
            
        except Exception as e:
            print(f"خطأ في إنشاء اختصار قائمة ابدأ: {e}")
    
    def register_program(self, install_path):
        """تسجيل البرنامج في النظام"""
        try:
            # تسجيل في Add/Remove Programs
            key_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\BitcoinAnalyzer"
            
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, key_path) as key:
                winreg.SetValueEx(key, "DisplayName", 0, winreg.REG_SZ, self.app_name)
                winreg.SetValueEx(key, "DisplayVersion", 0, winreg.REG_SZ, self.app_version)
                winreg.SetValueEx(key, "Publisher", 0, winreg.REG_SZ, self.app_publisher)
                winreg.SetValueEx(key, "InstallLocation", 0, winreg.REG_SZ, install_path)
                winreg.SetValueEx(key, "DisplayIcon", 0, winreg.REG_SZ, 
                                 os.path.join(install_path, "bitcoin_icon.ico"))
                
        except Exception as e:
            print(f"خطأ في تسجيل البرنامج: {e}")
    
    def run(self):
        """تشغيل المثبت"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    # التحقق من وجود الملفات المطلوبة
    required_files = [
        "dist/BitcoinAnalyzer.exe",
        "frontend/index.html",
        "backend/app.py",
        "bitcoin_icon.ico"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nيرجى التأكد من بناء البرنامج أولاً")
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل المثبت
    installer = BitcoinAnalyzerInstaller()
    installer.run()

if __name__ == "__main__":
    main()
