"""
Bitcoin AI Analyzer - Flask API
خادم Flask لتحليل البتكوين وتقديم التوصيات
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import sqlite3
import logging
from datetime import datetime, timedelta
import json
import os
from typing import Dict, List

# استيراد الوحدات المحلية
from utils import BitcoinDataFetcher
from analysis import TechnicalAnalyzer
from signals import TradingSignalGenerator

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# إنشاء تطبيق Flask
app = Flask(__name__)
CORS(app)  # السماح بطلبات CORS

# إعداد قاعدة البيانات
DATABASE_PATH = '../data/bitcoin_analysis.db'

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول التحليلات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS analyses (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        price REAL,
                        recommendation TEXT,
                        confidence REAL,
                        score REAL,
                        explanation TEXT,
                        indicators_data TEXT,
                        market_sentiment TEXT
                    )
                ''')
                
                # جدول الأسعار التاريخية
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS price_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        price REAL,
                        volume REAL,
                        price_change_24h REAL
                    )
                ''')
                
                conn.commit()
                logger.info("تم إنشاء قاعدة البيانات بنجاح")
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء قاعدة البيانات: {e}")
    
    def save_analysis(self, analysis_data: Dict):
        """حفظ نتائج التحليل"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO analyses 
                    (price, recommendation, confidence, score, explanation, 
                     indicators_data, market_sentiment)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    analysis_data.get('current_price'),
                    analysis_data.get('recommendation'),
                    analysis_data.get('confidence'),
                    analysis_data.get('score'),
                    analysis_data.get('explanation'),
                    json.dumps(analysis_data.get('individual_signals', {})),
                    json.dumps(analysis_data.get('market_sentiment', {}))
                ))
                
                conn.commit()
                logger.info("تم حفظ التحليل في قاعدة البيانات")
                
        except Exception as e:
            logger.error(f"خطأ في حفظ التحليل: {e}")
    
    def save_price_data(self, price_data: Dict):
        """حفظ بيانات السعر"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO price_history (price, volume, price_change_24h)
                    VALUES (?, ?, ?)
                ''', (
                    price_data.get('price'),
                    price_data.get('volume'),
                    price_data.get('price_change_percent')
                ))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات السعر: {e}")
    
    def get_recent_analyses(self, limit: int = 10) -> List[Dict]:
        """جلب التحليلات الأخيرة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT timestamp, price, recommendation, confidence, 
                           score, market_sentiment
                    FROM analyses 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                ''', (limit,))
                
                results = cursor.fetchall()
                
                analyses = []
                for row in results:
                    analyses.append({
                        'timestamp': row[0],
                        'price': row[1],
                        'recommendation': row[2],
                        'confidence': row[3],
                        'score': row[4],
                        'market_sentiment': json.loads(row[5]) if row[5] else {}
                    })
                
                return analyses
                
        except Exception as e:
            logger.error(f"خطأ في جلب التحليلات: {e}")
            return []

# إنشاء مدير قاعدة البيانات
db_manager = DatabaseManager(DATABASE_PATH)

# إنشاء كائنات التحليل
data_fetcher = BitcoinDataFetcher()
signal_generator = TradingSignalGenerator()

@app.route('/api/health', methods=['GET'])
def health_check():
    """فحص صحة الخادم"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'message': 'Bitcoin AI Analyzer is running'
    })

@app.route('/api/current-price', methods=['GET'])
def get_current_price():
    """جلب السعر الحالي للبتكوين"""
    try:
        current_price = data_fetcher.get_current_price()
        stats_24h = data_fetcher.get_24h_stats()
        
        if current_price is None:
            return jsonify({'error': 'فشل في جلب السعر الحالي'}), 500
        
        price_data = {
            'price': current_price,
            'timestamp': datetime.now().isoformat()
        }

        # إضافة إحصائيات 24 ساعة إذا كانت متوفرة
        if stats_24h:
            price_data.update(stats_24h)
        
        # حفظ بيانات السعر
        db_manager.save_price_data(price_data)
        
        return jsonify(price_data)
        
    except Exception as e:
        logger.error(f"خطأ في جلب السعر: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze', methods=['GET'])
def analyze_bitcoin():
    """تحليل البتكوين وإعطاء توصية"""
    try:
        # جلب المعاملات
        timeframe = request.args.get('timeframe', '1h')
        limit = int(request.args.get('limit', 200))
        
        logger.info(f"بدء تحليل البتكوين - الفترة: {timeframe}, العدد: {limit}")
        
        # جلب البيانات
        df = data_fetcher.get_klines(timeframe, limit)
        if df is None or df.empty:
            return jsonify({'error': 'فشل في جلب بيانات السوق'}), 500
        
        # حساب المؤشرات الفنية
        analyzer = TechnicalAnalyzer(df)
        indicators = analyzer.calculate_all_indicators()
        
        if not indicators:
            return jsonify({'error': 'فشل في حساب المؤشرات الفنية'}), 500
        
        # توليد إشارة التداول
        signal = signal_generator.generate_trading_signal(indicators)
        
        if 'error' in signal:
            return jsonify({'error': signal['error']}), 500
        
        # إضافة بيانات إضافية
        current_price = data_fetcher.get_current_price()
        signal['current_price'] = current_price
        signal['timeframe'] = timeframe
        signal['data_points'] = len(df)
        
        # حفظ التحليل
        db_manager.save_analysis(signal)
        
        logger.info(f"تم التحليل بنجاح - التوصية: {signal['recommendation']}")
        return jsonify(signal)
        
    except Exception as e:
        logger.error(f"خطأ في التحليل: {e}")
        return jsonify({'error': f'خطأ في التحليل: {str(e)}'}), 500

@app.route('/api/indicators', methods=['GET'])
def get_indicators():
    """جلب المؤشرات الفنية فقط"""
    try:
        timeframe = request.args.get('timeframe', '1h')
        limit = int(request.args.get('limit', 200))
        
        # جلب البيانات
        df = data_fetcher.get_klines(timeframe, limit)
        if df is None or df.empty:
            return jsonify({'error': 'فشل في جلب بيانات السوق'}), 500
        
        # حساب المؤشرات
        analyzer = TechnicalAnalyzer(df)
        indicators = analyzer.calculate_all_indicators()
        
        if not indicators:
            return jsonify({'error': 'فشل في حساب المؤشرات'}), 500
        
        return jsonify({
            'indicators': indicators,
            'timestamp': datetime.now().isoformat(),
            'timeframe': timeframe,
            'data_points': len(df)
        })
        
    except Exception as e:
        logger.error(f"خطأ في جلب المؤشرات: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/history', methods=['GET'])
def get_analysis_history():
    """جلب تاريخ التحليلات"""
    try:
        limit = int(request.args.get('limit', 20))
        analyses = db_manager.get_recent_analyses(limit)

        return jsonify({
            'analyses': analyses,
            'count': len(analyses),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"خطأ في جلب التاريخ: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/chart-data', methods=['GET'])
def get_chart_data():
    """جلب بيانات الرسم البياني"""
    try:
        timeframe = request.args.get('timeframe', '1h')
        limit = int(request.args.get('limit', 100))

        # جلب البيانات
        df = data_fetcher.get_klines(timeframe, limit)
        if df is None or df.empty:
            return jsonify({'error': 'فشل في جلب بيانات الرسم البياني'}), 500

        # تحويل البيانات لتنسيق مناسب للرسم البياني
        chart_data = []
        for index, row in df.iterrows():
            chart_data.append({
                'timestamp': index.isoformat(),
                'open': float(row['open']),
                'high': float(row['high']),
                'low': float(row['low']),
                'close': float(row['close']),
                'volume': float(row['volume'])
            })

        return jsonify({
            'data': chart_data,
            'timeframe': timeframe,
            'count': len(chart_data),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"خطأ في جلب بيانات الرسم البياني: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/multiple-timeframes', methods=['GET'])
def get_multiple_timeframes():
    """تحليل عدة فترات زمنية"""
    try:
        timeframes = ['1h', '4h', '1d']
        results = {}

        for tf in timeframes:
            try:
                df = data_fetcher.get_klines(tf, 100)
                if df is not None and not df.empty:
                    analyzer = TechnicalAnalyzer(df)
                    indicators = analyzer.calculate_all_indicators()

                    if indicators:
                        signal = signal_generator.generate_trading_signal(indicators)
                        results[tf] = {
                            'recommendation': signal.get('recommendation'),
                            'confidence': signal.get('confidence'),
                            'score': signal.get('score'),
                            'market_sentiment': signal.get('market_sentiment')
                        }
                    else:
                        results[tf] = {'error': 'فشل في حساب المؤشرات'}
                else:
                    results[tf] = {'error': 'فشل في جلب البيانات'}

            except Exception as e:
                results[tf] = {'error': str(e)}

        return jsonify({
            'timeframes': results,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"خطأ في تحليل الفترات المتعددة: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/market-overview', methods=['GET'])
def get_market_overview():
    """نظرة عامة على السوق"""
    try:
        # جلب البيانات الأساسية
        current_price = data_fetcher.get_current_price()
        stats_24h = data_fetcher.get_24h_stats()

        if current_price is None or stats_24h is None:
            return jsonify({'error': 'فشل في جلب بيانات السوق'}), 500

        # تحليل سريع
        df = data_fetcher.get_klines('1h', 50)
        quick_analysis = None

        if df is not None and not df.empty:
            analyzer = TechnicalAnalyzer(df)
            indicators = analyzer.calculate_all_indicators()

            if indicators:
                signal = signal_generator.generate_trading_signal(indicators)
                quick_analysis = {
                    'recommendation': signal.get('recommendation'),
                    'confidence': signal.get('confidence'),
                    'market_sentiment': signal.get('market_sentiment')
                }

        # جلب آخر التحليلات
        recent_analyses = db_manager.get_recent_analyses(5)

        overview = {
            'current_price': current_price,
            'price_change_24h': stats_24h.get('price_change_percent'),
            'volume_24h': stats_24h.get('volume'),
            'high_24h': stats_24h.get('high_price'),
            'low_24h': stats_24h.get('low_price'),
            'quick_analysis': quick_analysis,
            'recent_analyses': recent_analyses,
            'timestamp': datetime.now().isoformat()
        }

        return jsonify(overview)

    except Exception as e:
        logger.error(f"خطأ في جلب نظرة عامة على السوق: {e}")
        return jsonify({'error': str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    """معالج الخطأ 404"""
    return jsonify({'error': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(error):
    """معالج الخطأ 500"""
    return jsonify({'error': 'خطأ داخلي في الخادم'}), 500

if __name__ == '__main__':
    logger.info("🚀 بدء تشغيل Bitcoin AI Analyzer...")

    # اختبار الاتصال بالبيانات
    try:
        test_price = data_fetcher.get_current_price()
        if test_price:
            logger.info(f"✅ تم الاتصال بنجاح - السعر الحالي: ${test_price:,.2f}")
        else:
            logger.warning("⚠️ فشل في الاتصال بمصدر البيانات")
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الاتصال: {e}")

    # تشغيل الخادم
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
