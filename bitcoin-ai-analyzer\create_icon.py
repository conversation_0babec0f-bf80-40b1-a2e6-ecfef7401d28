#!/usr/bin/env python3
"""
إنشاء أيقونة البرنامج
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_bitcoin_icon():
    """إنشاء أيقونة البتكوين"""
    
    # إنشاء صورة بحجم 256x256
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان البتكوين
    bitcoin_orange = '#F7931A'
    bitcoin_dark = '#4D4D4D'
    white = '#FFFFFF'
    
    # رسم دائرة خلفية برتقالية
    margin = 10
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=bitcoin_orange, outline=bitcoin_dark, width=4)
    
    # رسم رمز البتكوين (₿)
    try:
        # محاولة استخدام خط نظام
        font_size = int(size * 0.6)
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("calibri.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        # رسم رمز B للبتكوين
        text = "₿"
        
        # حساب موقع النص في المنتصف
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - 10
        
        # رسم النص بلون أبيض مع حدود
        draw.text((x-2, y-2), text, font=font, fill=bitcoin_dark)
        draw.text((x+2, y-2), text, font=font, fill=bitcoin_dark)
        draw.text((x-2, y+2), text, font=font, fill=bitcoin_dark)
        draw.text((x+2, y+2), text, font=font, fill=bitcoin_dark)
        draw.text((x, y), text, font=font, fill=white)
        
    except Exception as e:
        print(f"خطأ في رسم النص: {e}")
        # رسم شكل بديل إذا فشل النص
        center = size // 2
        # رسم خطوط عمودية للبتكوين
        line_width = 8
        draw.rectangle([center-30, 40, center-30+line_width, size-40], fill=white)
        draw.rectangle([center+22, 40, center+22+line_width, size-40], fill=white)
        
        # رسم الخطوط الأفقية
        draw.rectangle([center-50, 80, center+50, 80+line_width], fill=white)
        draw.rectangle([center-50, center-10, center+50, center-10+line_width], fill=white)
        draw.rectangle([center-50, size-88, center+50, size-88+line_width], fill=white)
    
    return img

def create_multiple_sizes():
    """إنشاء أحجام متعددة للأيقونة"""
    base_icon = create_bitcoin_icon()
    
    # الأحجام المطلوبة لملف .ico
    sizes = [16, 24, 32, 48, 64, 128, 256]
    icons = []
    
    for size in sizes:
        resized = base_icon.resize((size, size), Image.Resampling.LANCZOS)
        icons.append(resized)
    
    return icons

def save_icon():
    """حفظ الأيقونة كملف .ico"""
    try:
        print("🎨 إنشاء أيقونة البرنامج...")
        
        icons = create_multiple_sizes()
        
        # حفظ كملف .ico
        icon_path = "bitcoin_icon.ico"
        icons[0].save(icon_path, format='ICO', sizes=[(icon.width, icon.height) for icon in icons])
        
        # حفظ كملف .png أيضاً للمعاينة
        png_path = "bitcoin_icon.png"
        icons[-1].save(png_path, format='PNG')
        
        print(f"✅ تم إنشاء الأيقونة: {icon_path}")
        print(f"✅ تم إنشاء معاينة PNG: {png_path}")
        
        return icon_path
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأيقونة: {e}")
        return None

if __name__ == "__main__":
    # تثبيت Pillow إذا لم تكن مثبتة
    try:
        from PIL import Image
    except ImportError:
        print("تثبيت مكتبة Pillow...")
        import subprocess
        subprocess.check_call(["pip", "install", "Pillow"])
        from PIL import Image, ImageDraw, ImageFont
    
    save_icon()
