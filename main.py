#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إنشاء شهادات التوقيع الرقمية (.p12)
Self-Signed Certificate Generator GUI Tool
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
import os
import tempfile
import shutil
from pathlib import Path
from config import get_setting, get_status_message, get_error_message

class CertificateGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title(get_setting('window_title'))
        self.root.geometry(get_setting('window_size'))
        self.root.resizable(get_setting('window_resizable'), get_setting('window_resizable'))
        
        # متغيرات الواجهة
        self.setup_variables()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # التحقق من OpenSSL
        self.check_openssl()
    
    def setup_variables(self):
        """إعداد متغيرات الواجهة"""
        self.name_var = tk.StringVar()
        self.country_var = tk.StringVar(value=get_setting('default_country'))
        self.state_var = tk.StringVar(value=get_setting('default_state'))
        self.city_var = tk.StringVar(value=get_setting('default_city'))
        self.org_var = tk.StringVar()
        self.email_var = tk.StringVar()
        self.days_var = tk.StringVar(value=get_setting('default_days'))
        self.password_var = tk.StringVar()
        self.openssl_path_var = tk.StringVar()
        self.output_dir_var = tk.StringVar(value=str(Path.home() / "Desktop"))
        self.open_folder_var = tk.BooleanVar(value=get_setting('auto_open_folder'))
        self.delete_temp_var = tk.BooleanVar(value=get_setting('temp_cleanup'))
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي مع شريط تمرير
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="مولد الشهادات الرقمية", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # إطار بيانات الشهادة
        cert_frame = ttk.LabelFrame(main_frame, text="بيانات الشهادة", padding=10)
        cert_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الاسم الكامل
        ttk.Label(cert_frame, text="الاسم الكامل (Common Name):").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(cert_frame, textvariable=self.name_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # الدولة
        ttk.Label(cert_frame, text="الدولة (Country):").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(cert_frame, textvariable=self.country_var, width=40).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # المنطقة/الولاية
        ttk.Label(cert_frame, text="المنطقة/الولاية (State):").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(cert_frame, textvariable=self.state_var, width=40).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # المدينة
        ttk.Label(cert_frame, text="المدينة (City):").grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Entry(cert_frame, textvariable=self.city_var, width=40).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # المؤسسة
        ttk.Label(cert_frame, text="المؤسسة (Organization):").grid(row=4, column=0, sticky=tk.W, pady=2)
        ttk.Entry(cert_frame, textvariable=self.org_var, width=40).grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # البريد الإلكتروني
        ttk.Label(cert_frame, text="البريد الإلكتروني (Email):").grid(row=5, column=0, sticky=tk.W, pady=2)
        ttk.Entry(cert_frame, textvariable=self.email_var, width=40).grid(row=5, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # مدة الصلاحية
        ttk.Label(cert_frame, text="مدة الصلاحية (أيام):").grid(row=6, column=0, sticky=tk.W, pady=2)
        ttk.Entry(cert_frame, textvariable=self.days_var, width=40).grid(row=6, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # كلمة مرور الشهادة
        ttk.Label(cert_frame, text="كلمة مرور الشهادة:").grid(row=7, column=0, sticky=tk.W, pady=2)
        ttk.Entry(cert_frame, textvariable=self.password_var, show="*", width=40).grid(row=7, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # إطار إعدادات OpenSSL
        openssl_frame = ttk.LabelFrame(main_frame, text="إعدادات OpenSSL", padding=10)
        openssl_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(openssl_frame, text="مسار OpenSSL:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(openssl_frame, textvariable=self.openssl_path_var, width=35).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Button(openssl_frame, text="تصفح", command=self.browse_openssl).grid(row=0, column=2, padx=(5, 0), pady=2)
        
        # إطار مجلد الحفظ
        output_frame = ttk.LabelFrame(main_frame, text="مجلد الحفظ", padding=10)
        output_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(output_frame, text="مجلد الحفظ:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(output_frame, textvariable=self.output_dir_var, width=35).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Button(output_frame, text="تصفح", command=self.browse_output_dir).grid(row=0, column=2, padx=(5, 0), pady=2)
        
        # إطار الخيارات
        options_frame = ttk.LabelFrame(main_frame, text="خيارات إضافية", padding=10)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(options_frame, text="فتح المجلد بعد الإنشاء", 
                       variable=self.open_folder_var).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="حذف الملفات المؤقتة بعد التصدير", 
                       variable=self.delete_temp_var).pack(anchor=tk.W)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إنشاء الشهادة", 
                  command=self.generate_certificate, 
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(buttons_frame, text="مسح الحقول", 
                  command=self.clear_fields).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(buttons_frame, text="خروج", 
                  command=self.root.quit).pack(side=tk.RIGHT)
        
        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز...")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def check_openssl(self):
        """التحقق من وجود OpenSSL"""
        # البحث في المسارات الشائعة من ملف التكوين
        common_paths = get_setting('openssl_paths')

        for path in common_paths:
            try:
                result = subprocess.run([path, "version"],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    self.openssl_path_var.set(path)
                    self.status_var.set(f"{get_status_message('openssl_found')}: {path}")
                    return True
            except:
                continue

        self.status_var.set(get_status_message('openssl_not_found'))
        return False

    def browse_openssl(self):
        """تصفح لاختيار ملف OpenSSL"""
        filename = filedialog.askopenfilename(
            title="اختر ملف OpenSSL",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if filename:
            self.openssl_path_var.set(filename)

    def browse_output_dir(self):
        """تصفح لاختيار مجلد الحفظ"""
        directory = filedialog.askdirectory(title="اختر مجلد الحفظ")
        if directory:
            self.output_dir_var.set(directory)

    def clear_fields(self):
        """مسح جميع الحقول"""
        self.name_var.set("")
        self.org_var.set("")
        self.email_var.set("")
        self.password_var.set("")
        self.days_var.set("365")
        self.status_var.set("تم مسح الحقول")

    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
            return False

        if not self.password_var.get():
            messagebox.showerror("خطأ", "يرجى إدخال كلمة مرور للشهادة")
            return False

        if not self.openssl_path_var.get():
            messagebox.showerror("خطأ", "يرجى تحديد مسار OpenSSL")
            return False

        try:
            days = int(self.days_var.get())
            if days <= 0:
                raise ValueError
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال عدد أيام صحيح")
            return False

        if not os.path.exists(self.output_dir_var.get()):
            messagebox.showerror("خطأ", "مجلد الحفظ غير موجود")
            return False

        return True

    def generate_certificate(self):
        """إنشاء الشهادة الرقمية"""
        if not self.validate_inputs():
            return

        try:
            self.status_var.set("جاري إنشاء الشهادة...")
            self.root.update()

            # إنشاء مجلد مؤقت
            with tempfile.TemporaryDirectory() as temp_dir:
                # مسارات الملفات المؤقتة
                key_file = os.path.join(temp_dir, "private.key")
                cert_file = os.path.join(temp_dir, "certificate.crt")
                p12_file = os.path.join(temp_dir, "certificate.p12")

                # إنشاء المفتاح الخاص
                self.status_var.set("إنشاء المفتاح الخاص...")
                self.root.update()

                key_cmd = [
                    self.openssl_path_var.get(),
                    "genrsa",
                    "-out", key_file,
                    "2048"
                ]

                result = subprocess.run(key_cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    raise Exception(f"فشل في إنشاء المفتاح الخاص: {result.stderr}")

                # إنشاء الشهادة
                self.status_var.set("إنشاء الشهادة...")
                self.root.update()

                # إعداد معلومات الشهادة
                subject = f"/C={self.country_var.get()}/ST={self.state_var.get()}/L={self.city_var.get()}/O={self.org_var.get()}/CN={self.name_var.get()}"
                if self.email_var.get():
                    subject += f"/emailAddress={self.email_var.get()}"

                cert_cmd = [
                    self.openssl_path_var.get(),
                    "req",
                    "-new",
                    "-x509",
                    "-key", key_file,
                    "-out", cert_file,
                    "-days", self.days_var.get(),
                    "-subj", subject
                ]

                result = subprocess.run(cert_cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    raise Exception(f"فشل في إنشاء الشهادة: {result.stderr}")

                # تحويل إلى P12
                self.status_var.set("تحويل إلى صيغة P12...")
                self.root.update()

                p12_cmd = [
                    self.openssl_path_var.get(),
                    "pkcs12",
                    "-export",
                    "-out", p12_file,
                    "-inkey", key_file,
                    "-in", cert_file,
                    "-passout", f"pass:{self.password_var.get()}"
                ]

                result = subprocess.run(p12_cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    raise Exception(f"فشل في تحويل الشهادة إلى P12: {result.stderr}")

                # نسخ الملف النهائي
                final_filename = f"{self.name_var.get().replace(' ', '_')}_certificate.p12"
                final_path = os.path.join(self.output_dir_var.get(), final_filename)

                # التأكد من عدم وجود ملف بنفس الاسم
                counter = 1
                while os.path.exists(final_path):
                    name_part = f"{self.name_var.get().replace(' ', '_')}_certificate_{counter}.p12"
                    final_path = os.path.join(self.output_dir_var.get(), name_part)
                    counter += 1

                shutil.copy2(p12_file, final_path)

                # رسالة نجاح
                success_msg = f"تم إنشاء الشهادة بنجاح!\n\nمسار الملف:\n{final_path}\n\nكلمة المرور: {self.password_var.get()}"
                messagebox.showinfo("نجح", success_msg)

                self.status_var.set(f"تم إنشاء الشهادة: {os.path.basename(final_path)}")

                # فتح المجلد إذا كان مطلوباً
                if self.open_folder_var.get():
                    os.startfile(self.output_dir_var.get())

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الشهادة:\n{str(e)}")
            self.status_var.set("فشل في إنشاء الشهادة")


def main():
    """الدالة الرئيسية"""
    root = tk.Tk()

    # تطبيق ثيم حديث
    style = ttk.Style()
    style.theme_use('winnative')

    CertificateGenerator(root)

    # تشغيل التطبيق
    root.mainloop()


if __name__ == "__main__":
    main()
