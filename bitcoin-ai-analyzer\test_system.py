#!/usr/bin/env python3
"""
اختبار سريع لنظام تحليل البتكوين
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🔍 اختبار استيراد المكتبات...")
    
    try:
        import pandas as pd
        print("✅ pandas: نجح")
    except ImportError as e:
        print(f"❌ pandas: فشل - {e}")
        return False
    
    try:
        import numpy as np
        print("✅ numpy: نجح")
    except ImportError as e:
        print(f"❌ numpy: فشل - {e}")
        return False
    
    try:
        import ta
        print("✅ ta: نجح")
    except ImportError as e:
        print(f"❌ ta: فشل - {e}")
        return False
    
    try:
        import requests
        print("✅ requests: نجح")
    except ImportError as e:
        print(f"❌ requests: فشل - {e}")
        return False
    
    try:
        from flask import Flask
        print("✅ Flask: نجح")
    except ImportError as e:
        print(f"❌ Flask: فشل - {e}")
        return False
    
    return True

def test_data_fetcher():
    """اختبار جلب البيانات"""
    print("\n📊 اختبار جلب البيانات...")
    
    try:
        from utils import BitcoinDataFetcher
        
        fetcher = BitcoinDataFetcher()
        
        # اختبار السعر الحالي
        price = fetcher.get_current_price()
        if price and price > 0:
            print(f"✅ السعر الحالي: ${price:,.2f}")
        else:
            print("❌ فشل في جلب السعر الحالي")
            return False
        
        # اختبار بيانات الشموع
        df = fetcher.get_klines('1h', 50)
        if df is not None and not df.empty:
            print(f"✅ بيانات الشموع: {len(df)} شمعة")
        else:
            print("❌ فشل في جلب بيانات الشموع")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار جلب البيانات: {e}")
        return False

def test_technical_analysis():
    """اختبار التحليل الفني"""
    print("\n📈 اختبار التحليل الفني...")
    
    try:
        from utils import BitcoinDataFetcher
        from analysis import TechnicalAnalyzer
        
        # جلب البيانات
        fetcher = BitcoinDataFetcher()
        df = fetcher.get_klines('1h', 100)
        
        if df is None or df.empty:
            print("❌ لا توجد بيانات للتحليل")
            return False
        
        # إجراء التحليل
        analyzer = TechnicalAnalyzer(df)
        indicators = analyzer.calculate_all_indicators()
        
        if indicators:
            print("✅ المؤشرات الفنية:")
            for name, data in indicators.items():
                if 'interpretation' in data:
                    print(f"   📊 {name.upper()}: {data['interpretation'][:50]}...")
            return True
        else:
            print("❌ فشل في حساب المؤشرات الفنية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التحليل الفني: {e}")
        return False

def test_signal_generation():
    """اختبار توليد الإشارات"""
    print("\n🎯 اختبار توليد الإشارات...")
    
    try:
        from utils import BitcoinDataFetcher
        from analysis import TechnicalAnalyzer
        from signals import TradingSignalGenerator
        
        # جلب البيانات وحساب المؤشرات
        fetcher = BitcoinDataFetcher()
        df = fetcher.get_klines('1h', 100)
        
        if df is None or df.empty:
            print("❌ لا توجد بيانات للتحليل")
            return False
        
        analyzer = TechnicalAnalyzer(df)
        indicators = analyzer.calculate_all_indicators()
        
        if not indicators:
            print("❌ فشل في حساب المؤشرات")
            return False
        
        # توليد الإشارة
        signal_generator = TradingSignalGenerator()
        signal = signal_generator.generate_trading_signal(indicators)
        
        if 'error' not in signal:
            print("✅ إشارة التداول:")
            print(f"   🎯 التوصية: {signal['recommendation']}")
            print(f"   📊 مستوى الثقة: {signal['confidence']:.1%}")
            print(f"   💭 المعنويات: {signal['market_sentiment']['sentiment']}")
            return True
        else:
            print(f"❌ خطأ في توليد الإشارة: {signal['error']}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار توليد الإشارات: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        import sqlite3
        import os
        
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        os.makedirs(data_dir, exist_ok=True)
        
        # اختبار إنشاء قاعدة البيانات
        db_path = os.path.join(data_dir, 'test.db')
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY, name TEXT)')
            cursor.execute('INSERT INTO test (name) VALUES (?)', ('test_entry',))
            conn.commit()
            
            cursor.execute('SELECT COUNT(*) FROM test')
            count = cursor.fetchone()[0]
            
        # حذف ملف الاختبار
        try:
            if os.path.exists(db_path):
                os.remove(db_path)
        except Exception:
            pass  # تجاهل أخطاء الحذف
        
        if count > 0:
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام تحليل البتكوين الذكي")
    print("=" * 50)
    
    tests = [
        ("استيراد المكتبات", test_imports),
        ("جلب البيانات", test_data_fetcher),
        ("التحليل الفني", test_technical_analysis),
        ("توليد الإشارات", test_signal_generation),
        ("قاعدة البيانات", test_database)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("\n📋 خطوات التشغيل:")
        print("1. cd backend")
        print("2. python app.py")
        print("3. افتح frontend/index.html في المتصفح")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        
        if passed == 0:
            print("\n💡 نصائح لحل المشاكل:")
            print("- تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
            print("- تأكد من اتصال الإنترنت لجلب البيانات")
            print("- تأكد من إصدار Python 3.10 أو أحدث")

if __name__ == "__main__":
    main()
