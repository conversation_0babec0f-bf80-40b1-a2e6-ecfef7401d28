# 🚀 دليل المستخدم - محلل البتكوين الذكي

## 📋 نظرة عامة

**محلل البتكوين الذكي** هو برنامج احترافي لتحليل سوق البتكوين باستخدام المؤشرات الفنية المتقدمة. يقدم البرنامج توصيات ذكية للتداول مع تفسير مفصل للأسباب.

## 🎯 المميزات الرئيسية

### 📊 التحليل الفني الشامل
- **مؤشر القوة النسبية (RSI)**: تحديد حالات التشبع البيعي والشرائي
- **مؤشر MACD**: تحليل الزخم واتجاه السوق  
- **المتوسطات المتحركة (EMA)**: تحديد الاتجاه العام
- **نطاقات بولينجر**: قياس التقلبات والدعم والمقاومة
- **تحليل الحجم**: تأكيد الاتجاهات والانعكاسات

### 🎯 نظام التوصيات الذكي
- **شراء قوي**: إشارات إيجابية قوية مع ثقة عالية
- **شراء**: إشارات إيجابية متوسطة
- **انتظار**: السوق في حالة تذبذب
- **بيع**: إشارات سلبية متوسطة  
- **بيع قوي**: إشارات سلبية قوية مع ثقة عالية

## 🚀 طرق التشغيل

### الطريقة الأولى: ملف exe (الأسهل)
1. انقر نقراً مزدوجاً على ملف `تشغيل_البرنامج.bat`
2. أو انقر مباشرة على `dist/BitcoinAnalyzer.exe`
3. سيتم فتح المتصفح تلقائياً مع الواجهة

### الطريقة الثانية: من الكود المصدري
```bash
# تثبيت المتطلبات (مرة واحدة فقط)
pip install -r requirements.txt

# تشغيل البرنامج
python main.py
```

## 🖥️ واجهة المستخدم

### 1. لوحة السعر
- **السعر الحالي**: سعر البتكوين اللحظي
- **التغيير 24 ساعة**: نسبة التغيير خلال 24 ساعة
- **أعلى/أقل سعر**: أعلى وأقل سعر خلال 24 ساعة
- **الحجم**: حجم التداول خلال 24 ساعة

### 2. لوحة التوصيات
- **التوصية الحالية**: شراء/بيع/انتظار
- **مستوى الثقة**: نسبة مئوية تعبر عن قوة الإشارة
- **التفسير**: شرح مفصل لأسباب التوصية
- **معنويات السوق**: الحالة العامة للسوق

### 3. المؤشرات الفنية
- **RSI**: مؤشر القوة النسبية مع شريط مرئي
- **MACD**: خطوط MACD والإشارة مع الهيستوجرام
- **EMA**: المتوسطات المتحركة 50 و 200
- **Bollinger Bands**: النطاقات العلوية والسفلية
- **Volume**: تحليل حجم التداول

### 4. الرسم البياني
- رسم بياني تفاعلي لأسعار البتكوين
- إمكانية تغيير الفترة الزمنية (1 ساعة، 4 ساعات، يوم)
- أدوات التكبير والتصغير

### 5. تاريخ التحليلات
- عرض آخر 10 تحليلات
- تاريخ ووقت كل تحليل
- التوصية ومستوى الثقة

## ⚙️ الإعدادات والتخصيص

### تبديل الوضع الليلي/النهاري
- انقر على أيقونة القمر/الشمس في الأعلى
- يتم حفظ الإعداد تلقائياً

### تحديث التحليل
- انقر على زر "حلل الآن" للحصول على تحليل فوري
- التحديث التلقائي كل 5 دقائق

### تغيير الفترة الزمنية
- استخدم القائمة المنسدلة في قسم الرسم البياني
- الخيارات: 1 ساعة، 4 ساعات، يوم واحد

## 📊 فهم التوصيات

### مؤشرات الشراء
- **RSI أقل من 30**: تشبع بيعي
- **MACD تقاطع صاعد**: زخم إيجابي
- **السعر فوق EMA**: اتجاه صاعد
- **السعر تحت النطاق السفلي**: فرصة شراء

### مؤشرات البيع  
- **RSI أكبر من 70**: تشبع شرائي
- **MACD تقاطع هابط**: زخم سلبي
- **السعر تحت EMA**: اتجاه هابط
- **السعر فوق النطاق العلوي**: فرصة بيع

### مؤشرات الانتظار
- **RSI بين 30-70**: منطقة متوازنة
- **إشارات متضاربة**: عدم وضوح الاتجاه
- **حجم تداول منخفض**: عدم تأكيد الاتجاه

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. البرنامج لا يبدأ**
- تأكد من اتصال الإنترنت
- أعد تشغيل البرنامج
- تحقق من إعدادات مكافح الفيروسات

**2. لا تظهر البيانات**
- تحقق من اتصال الإنترنت
- انتظر قليلاً (قد يستغرق جلب البيانات وقتاً)
- انقر على "حلل الآن" لإعادة المحاولة

**3. الواجهة لا تفتح**
- تأكد من أن المنفذ 8000 غير مستخدم
- أعد تشغيل البرنامج
- افتح المتصفح يدوياً واذهب إلى `http://localhost:8000`

**4. خطأ في المكتبات (عند التشغيل من الكود)**
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

## ⚠️ تنبيهات مهمة

### إخلاء المسؤولية
1. **هذا البرنامج لأغراض تعليمية وتحليلية فقط**
2. **ليس نصيحة استثمارية أو مالية**
3. **استشر خبير مالي قبل اتخاذ قرارات استثمارية**
4. **الأسواق المالية محفوفة بالمخاطر**
5. **لا تستثمر أكثر مما يمكنك تحمل خسارته**

### نصائح للاستخدام الآمن
- لا تعتمد على توصية واحدة فقط
- راجع عدة مصادر قبل اتخاذ القرار
- ابدأ بمبالغ صغيرة للتعلم
- تعلم أساسيات التحليل الفني
- لا تتداول بناءً على العواطف

## 📞 الدعم والمساعدة

### الملفات المهمة
- `README.md`: دليل تقني مفصل
- `requirements.txt`: قائمة المكتبات المطلوبة
- `test_system.py`: اختبار النظام

### معلومات تقنية
- **اللغة**: Python 3.10+
- **إطار العمل**: Flask + HTML/CSS/JavaScript
- **مصدر البيانات**: Binance API
- **قاعدة البيانات**: SQLite

### نصائح للمطورين
- يمكن تخصيص المؤشرات في `backend/analysis.py`
- يمكن تعديل خوارزمية القرار في `backend/signals.py`
- يمكن تغيير التصميم في `frontend/styles.css`

---

**تم تطوير هذا البرنامج بـ ❤️ لمجتمع التحليل الفني والعملات الرقمية**

**إصدار البرنامج**: 1.0  
**تاريخ الإنشاء**: 2025  
**المطور**: AI Assistant
