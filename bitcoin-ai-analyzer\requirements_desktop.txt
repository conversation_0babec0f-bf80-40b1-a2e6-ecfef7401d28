# متطلبات تطبيق سطح المكتب - مح<PERSON><PERSON> البتكوين الذكي
# Desktop Application Requirements - Bitcoin AI Analyzer

# مكتبات التحليل الفني والبيانات
# Technical Analysis and Data Libraries
pandas>=2.0.0
numpy>=1.24.0
ta>=0.10.2
requests>=2.31.0

# مكتبات الواجهة الرسومية
# GUI Libraries
matplotlib>=3.7.0
tkinter  # مدمجة مع Python

# مكتبات إضافية للرسوم البيانية
# Additional Chart Libraries
Pillow>=10.0.0

# مكتبات قاعدة البيانات
# Database Libraries
sqlite3  # مدمجة مع Python

# مكتبات التاريخ والوقت
# Date and Time Libraries
python-dateutil>=2.8.0

# مكتبات التشفير والأمان (اختيارية)
# Encryption and Security Libraries (Optional)
cryptography>=41.0.0

# مكتبات إنشاء ملف exe
# Executable Creation Libraries
pyinstaller>=5.13.0

# مكتبات إضافية للأداء
# Additional Performance Libraries
numba>=0.57.0  # تسريع العمليات الحسابية
cython>=3.0.0  # تحسين الأداء
