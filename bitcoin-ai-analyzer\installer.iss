; Bitcoin AI Analyzer - Inno Setup Script
; محلل البتكوين الذكي - سكريبت التثبيت

[Setup]
; معلومات التطبيق الأساسية
AppId={{B7C8F9E2-4A3D-4E5F-8C9B-1A2B3C4D5E6F}
AppName=Bitcoin AI Analyzer
AppVersion=1.0
AppVerName=محلل البتكوين الذكي 1.0
AppPublisher=Bitcoin AI Team
AppPublisherURL=https://github.com/bitcoin-ai-analyzer
AppSupportURL=https://github.com/bitcoin-ai-analyzer/support
AppUpdatesURL=https://github.com/bitcoin-ai-analyzer/updates

; مجلد التثبيت الافتراضي
DefaultDirName={autopf}\Bitcoin AI Analyzer
DefaultGroupName=Bitcoin AI Analyzer
AllowNoIcons=yes

; ملف الإخراج
OutputDir=installer_output
OutputBaseFilename=BitcoinAnalyzer_Setup
SetupIconFile=bitcoin_icon.ico

; ضغط البيانات
Compression=lzma
SolidCompression=yes

; دعم Windows 10/11
WizardStyle=modern
MinVersion=10.0

; الترخيص والمعلومات
LicenseFile=LICENSE.txt
InfoBeforeFile=INSTALL_INFO.txt
InfoAfterFile=USAGE_INFO.txt

; إعدادات إضافية
PrivilegesRequired=lowest
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
; الملف التنفيذي الرئيسي
Source: "dist\BitcoinAnalyzer.exe"; DestDir: "{app}"; Flags: ignoreversion

; ملفات الواجهة الأمامية
Source: "frontend\*"; DestDir: "{app}\frontend"; Flags: ignoreversion recursesubdirs createallsubdirs

; ملفات الخلفية
Source: "backend\*"; DestDir: "{app}\backend"; Flags: ignoreversion recursesubdirs createallsubdirs

; مجلد البيانات
Source: "data\*"; DestDir: "{app}\data"; Flags: ignoreversion recursesubdirs createallsubdirs

; ملفات التوثيق
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "دليل_المستخدم.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "ملخص_المشروع.md"; DestDir: "{app}"; Flags: ignoreversion

; ملفات التكوين
Source: "requirements.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "main.py"; DestDir: "{app}"; Flags: ignoreversion

; الأيقونة
Source: "bitcoin_icon.ico"; DestDir: "{app}"; Flags: ignoreversion
Source: "bitcoin_icon.png"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
; اختصار في قائمة ابدأ
Name: "{group}\Bitcoin AI Analyzer"; Filename: "{app}\BitcoinAnalyzer.exe"; IconFilename: "{app}\bitcoin_icon.ico"; Comment: "محلل البتكوين الذكي - تحليل فني احترافي"

; اختصار على سطح المكتب
Name: "{autodesktop}\Bitcoin AI Analyzer"; Filename: "{app}\BitcoinAnalyzer.exe"; IconFilename: "{app}\bitcoin_icon.ico"; Comment: "محلل البتكوين الذكي"; Tasks: desktopicon

; اختصار في شريط المهام السريع
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\Bitcoin AI Analyzer"; Filename: "{app}\BitcoinAnalyzer.exe"; IconFilename: "{app}\bitcoin_icon.ico"; Tasks: quicklaunchicon

; اختصار لدليل المستخدم
Name: "{group}\دليل المستخدم"; Filename: "{app}\دليل_المستخدم.md"; Comment: "دليل استخدام البرنامج"

; اختصار لإلغاء التثبيت
Name: "{group}\{cm:UninstallProgram,Bitcoin AI Analyzer}"; Filename: "{uninstallexe}"

[Run]
; تشغيل البرنامج بعد التثبيت
Filename: "{app}\BitcoinAnalyzer.exe"; Description: "{cm:LaunchProgram,Bitcoin AI Analyzer}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
; حذف ملفات إضافية عند إلغاء التثبيت
Type: filesandordirs; Name: "{app}\data\*.db"
Type: filesandordirs; Name: "{app}\__pycache__"
Type: filesandordirs; Name: "{app}\backend\__pycache__"

[Code]
// كود Pascal لوظائف إضافية

function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

[Messages]
; رسائل مخصصة
WelcomeLabel2=سيقوم هذا المعالج بتثبيت [name/ver] على جهاز الكمبيوتر الخاص بك.%n%nمحلل البتكوين الذكي هو برنامج احترافي لتحليل سوق البتكوين باستخدام المؤشرات الفنية المتقدمة.%n%nيُنصح بإغلاق جميع التطبيقات الأخرى قبل المتابعة.

[CustomMessages]
; رسائل مخصصة إضافية
LaunchProgram=تشغيل محلل البتكوين الذكي
CreateDesktopIcon=إنشاء اختصار على سطح المكتب
CreateQuickLaunchIcon=إنشاء اختصار في شريط المهام السريع
