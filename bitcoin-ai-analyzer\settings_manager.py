#!/usr/bin/env python3
"""
Settings Manager for Bitcoin AI Analyzer
مدير الإعدادات لمحلل البتكوين الذكي
"""

import json
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime

class SettingsManager:
    """مدير الإعدادات"""
    
    def __init__(self):
        self.settings_file = "settings.json"
        self.default_settings = {
            "theme": "light",  # light, dark
            "auto_refresh": True,
            "refresh_interval": 300,  # seconds
            "default_timeframe": "1h",
            "show_notifications": True,
            "sound_alerts": False,
            "chart_style": "candlestick",  # candlestick, line
            "language": "ar",  # ar, en
            "window_geometry": "1200x800",
            "confidence_threshold": 70.0,
            "risk_level": "medium",  # low, medium, high
            "data_source": "binance",
            "save_history": True,
            "max_history_items": 100,
            "export_format": "csv",  # csv, json, excel
            "backup_enabled": True,
            "backup_interval": 24,  # hours
            "advanced_indicators": False,
            "debug_mode": False
        }
        self.settings = self.load_settings()
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # دمج الإعدادات المحملة مع الافتراضية
                settings = self.default_settings.copy()
                settings.update(loaded_settings)
                return settings
            else:
                return self.default_settings.copy()
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            return self.default_settings.copy()
    
    def save_settings(self):
        """حفظ الإعدادات إلى الملف"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def get(self, key, default=None):
        """الحصول على قيمة إعداد"""
        return self.settings.get(key, default)
    
    def set(self, key, value):
        """تعيين قيمة إعداد"""
        self.settings[key] = value
        self.save_settings()
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        self.settings = self.default_settings.copy()
        self.save_settings()
    
    def export_settings(self, file_path):
        """تصدير الإعدادات إلى ملف"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"خطأ في تصدير الإعدادات: {e}")
            return False
    
    def import_settings(self, file_path):
        """استيراد الإعدادات من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # التحقق من صحة الإعدادات
            valid_settings = {}
            for key, value in imported_settings.items():
                if key in self.default_settings:
                    valid_settings[key] = value
            
            self.settings.update(valid_settings)
            self.save_settings()
            return True
        except Exception as e:
            print(f"خطأ في استيراد الإعدادات: {e}")
            return False

class SettingsWindow:
    """نافذة الإعدادات"""
    
    def __init__(self, parent, settings_manager, on_settings_changed=None):
        self.parent = parent
        self.settings_manager = settings_manager
        self.on_settings_changed = on_settings_changed
        
        self.window = tk.Toplevel(parent)
        self.window.title("⚙️ الإعدادات - Settings")
        self.window.geometry("600x700")
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
        
        # متغيرات الإعدادات
        self.setup_variables()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل القيم الحالية
        self.load_current_settings()
        
        # توسيط النافذة
        self.center_window()
    
    def setup_variables(self):
        """إعداد متغيرات الإعدادات"""
        self.theme_var = tk.StringVar()
        self.auto_refresh_var = tk.BooleanVar()
        self.refresh_interval_var = tk.IntVar()
        self.default_timeframe_var = tk.StringVar()
        self.show_notifications_var = tk.BooleanVar()
        self.sound_alerts_var = tk.BooleanVar()
        self.chart_style_var = tk.StringVar()
        self.confidence_threshold_var = tk.DoubleVar()
        self.risk_level_var = tk.StringVar()
        self.save_history_var = tk.BooleanVar()
        self.max_history_var = tk.IntVar()
        self.advanced_indicators_var = tk.BooleanVar()
        self.debug_mode_var = tk.BooleanVar()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء notebook للتبويبات
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # تبويب العام
        self.create_general_tab()
        
        # تبويب العرض
        self.create_display_tab()
        
        # تبويب التحليل
        self.create_analysis_tab()
        
        # تبويب البيانات
        self.create_data_tab()
        
        # تبويب متقدم
        self.create_advanced_tab()
        
        # أزرار التحكم
        self.create_control_buttons()
    
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        general_frame = ttk.Frame(self.notebook)
        self.notebook.add(general_frame, text="عام")
        
        # التحديث التلقائي
        refresh_frame = ttk.LabelFrame(general_frame, text="التحديث التلقائي", padding=10)
        refresh_frame.pack(fill='x', pady=5)
        
        ttk.Checkbutton(refresh_frame, text="تفعيل التحديث التلقائي",
                       variable=self.auto_refresh_var).pack(anchor='w')
        
        interval_frame = ttk.Frame(refresh_frame)
        interval_frame.pack(fill='x', pady=5)
        
        ttk.Label(interval_frame, text="فترة التحديث (ثانية):").pack(side='left')
        ttk.Spinbox(interval_frame, from_=60, to=3600, width=10,
                   textvariable=self.refresh_interval_var).pack(side='right')
        
        # الفترة الزمنية الافتراضية
        timeframe_frame = ttk.LabelFrame(general_frame, text="الفترة الزمنية الافتراضية", padding=10)
        timeframe_frame.pack(fill='x', pady=5)
        
        timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']
        ttk.Combobox(timeframe_frame, textvariable=self.default_timeframe_var,
                    values=timeframes, state='readonly').pack(anchor='w')
        
        # التنبيهات
        alerts_frame = ttk.LabelFrame(general_frame, text="التنبيهات", padding=10)
        alerts_frame.pack(fill='x', pady=5)
        
        ttk.Checkbutton(alerts_frame, text="إظهار الإشعارات",
                       variable=self.show_notifications_var).pack(anchor='w')
        
        ttk.Checkbutton(alerts_frame, text="تنبيهات صوتية",
                       variable=self.sound_alerts_var).pack(anchor='w')
    
    def create_display_tab(self):
        """إنشاء تبويب إعدادات العرض"""
        display_frame = ttk.Frame(self.notebook)
        self.notebook.add(display_frame, text="العرض")
        
        # الثيم
        theme_frame = ttk.LabelFrame(display_frame, text="المظهر", padding=10)
        theme_frame.pack(fill='x', pady=5)
        
        ttk.Radiobutton(theme_frame, text="وضع فاتح", value="light",
                       variable=self.theme_var).pack(anchor='w')
        ttk.Radiobutton(theme_frame, text="وضع مظلم", value="dark",
                       variable=self.theme_var).pack(anchor='w')
        
        # نمط الرسم البياني
        chart_frame = ttk.LabelFrame(display_frame, text="الرسم البياني", padding=10)
        chart_frame.pack(fill='x', pady=5)
        
        ttk.Radiobutton(chart_frame, text="شموع يابانية", value="candlestick",
                       variable=self.chart_style_var).pack(anchor='w')
        ttk.Radiobutton(chart_frame, text="خط بسيط", value="line",
                       variable=self.chart_style_var).pack(anchor='w')
    
    def create_analysis_tab(self):
        """إنشاء تبويب إعدادات التحليل"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="التحليل")
        
        # عتبة الثقة
        confidence_frame = ttk.LabelFrame(analysis_frame, text="عتبة الثقة", padding=10)
        confidence_frame.pack(fill='x', pady=5)
        
        ttk.Label(confidence_frame, text="الحد الأدنى للثقة (%):").pack(anchor='w')
        ttk.Scale(confidence_frame, from_=50, to=95, orient='horizontal',
                 variable=self.confidence_threshold_var).pack(fill='x', pady=5)
        
        confidence_value_label = ttk.Label(confidence_frame, text="")
        confidence_value_label.pack(anchor='w')
        
        def update_confidence_label(*args):
            confidence_value_label.config(text=f"{self.confidence_threshold_var.get():.1f}%")
        
        self.confidence_threshold_var.trace('w', update_confidence_label)
        
        # مستوى المخاطرة
        risk_frame = ttk.LabelFrame(analysis_frame, text="مستوى المخاطرة", padding=10)
        risk_frame.pack(fill='x', pady=5)
        
        ttk.Radiobutton(risk_frame, text="منخفض", value="low",
                       variable=self.risk_level_var).pack(anchor='w')
        ttk.Radiobutton(risk_frame, text="متوسط", value="medium",
                       variable=self.risk_level_var).pack(anchor='w')
        ttk.Radiobutton(risk_frame, text="عالي", value="high",
                       variable=self.risk_level_var).pack(anchor='w')
        
        # مؤشرات متقدمة
        indicators_frame = ttk.LabelFrame(analysis_frame, text="المؤشرات", padding=10)
        indicators_frame.pack(fill='x', pady=5)
        
        ttk.Checkbutton(indicators_frame, text="تفعيل المؤشرات المتقدمة",
                       variable=self.advanced_indicators_var).pack(anchor='w')
    
    def create_data_tab(self):
        """إنشاء تبويب إعدادات البيانات"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="البيانات")
        
        # حفظ التاريخ
        history_frame = ttk.LabelFrame(data_frame, text="تاريخ التحليلات", padding=10)
        history_frame.pack(fill='x', pady=5)
        
        ttk.Checkbutton(history_frame, text="حفظ تاريخ التحليلات",
                       variable=self.save_history_var).pack(anchor='w')
        
        max_history_frame = ttk.Frame(history_frame)
        max_history_frame.pack(fill='x', pady=5)
        
        ttk.Label(max_history_frame, text="الحد الأقصى للعناصر:").pack(side='left')
        ttk.Spinbox(max_history_frame, from_=10, to=1000, width=10,
                   textvariable=self.max_history_var).pack(side='right')
    
    def create_advanced_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(advanced_frame, text="متقدم")
        
        # وضع التطوير
        debug_frame = ttk.LabelFrame(advanced_frame, text="التطوير", padding=10)
        debug_frame.pack(fill='x', pady=5)
        
        ttk.Checkbutton(debug_frame, text="تفعيل وضع التطوير",
                       variable=self.debug_mode_var).pack(anchor='w')
        
        # إدارة الإعدادات
        management_frame = ttk.LabelFrame(advanced_frame, text="إدارة الإعدادات", padding=10)
        management_frame.pack(fill='x', pady=5)
        
        ttk.Button(management_frame, text="تصدير الإعدادات",
                  command=self.export_settings).pack(fill='x', pady=2)
        
        ttk.Button(management_frame, text="استيراد الإعدادات",
                  command=self.import_settings).pack(fill='x', pady=2)
        
        ttk.Button(management_frame, text="إعادة تعيين إلى الافتراضي",
                  command=self.reset_settings).pack(fill='x', pady=2)
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        button_frame = ttk.Frame(self.window)
        button_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(button_frame, text="إلغاء", command=self.cancel).pack(side='left')
        ttk.Button(button_frame, text="تطبيق", command=self.apply_settings).pack(side='right', padx=(5, 0))
        ttk.Button(button_frame, text="موافق", command=self.ok).pack(side='right')
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        self.theme_var.set(self.settings_manager.get('theme'))
        self.auto_refresh_var.set(self.settings_manager.get('auto_refresh'))
        self.refresh_interval_var.set(self.settings_manager.get('refresh_interval'))
        self.default_timeframe_var.set(self.settings_manager.get('default_timeframe'))
        self.show_notifications_var.set(self.settings_manager.get('show_notifications'))
        self.sound_alerts_var.set(self.settings_manager.get('sound_alerts'))
        self.chart_style_var.set(self.settings_manager.get('chart_style'))
        self.confidence_threshold_var.set(self.settings_manager.get('confidence_threshold'))
        self.risk_level_var.set(self.settings_manager.get('risk_level'))
        self.save_history_var.set(self.settings_manager.get('save_history'))
        self.max_history_var.set(self.settings_manager.get('max_history_items'))
        self.advanced_indicators_var.set(self.settings_manager.get('advanced_indicators'))
        self.debug_mode_var.set(self.settings_manager.get('debug_mode'))
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        self.settings_manager.set('theme', self.theme_var.get())
        self.settings_manager.set('auto_refresh', self.auto_refresh_var.get())
        self.settings_manager.set('refresh_interval', self.refresh_interval_var.get())
        self.settings_manager.set('default_timeframe', self.default_timeframe_var.get())
        self.settings_manager.set('show_notifications', self.show_notifications_var.get())
        self.settings_manager.set('sound_alerts', self.sound_alerts_var.get())
        self.settings_manager.set('chart_style', self.chart_style_var.get())
        self.settings_manager.set('confidence_threshold', self.confidence_threshold_var.get())
        self.settings_manager.set('risk_level', self.risk_level_var.get())
        self.settings_manager.set('save_history', self.save_history_var.get())
        self.settings_manager.set('max_history_items', self.max_history_var.get())
        self.settings_manager.set('advanced_indicators', self.advanced_indicators_var.get())
        self.settings_manager.set('debug_mode', self.debug_mode_var.get())
        
        if self.on_settings_changed:
            self.on_settings_changed()
        
        messagebox.showinfo("تم", "تم تطبيق الإعدادات بنجاح!")
    
    def ok(self):
        """موافق وإغلاق"""
        self.apply_settings()
        self.window.destroy()
    
    def cancel(self):
        """إلغاء وإغلاق"""
        self.window.destroy()
    
    def export_settings(self):
        """تصدير الإعدادات"""
        file_path = filedialog.asksaveasfilename(
            title="تصدير الإعدادات",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            if self.settings_manager.export_settings(file_path):
                messagebox.showinfo("تم", "تم تصدير الإعدادات بنجاح!")
            else:
                messagebox.showerror("خطأ", "فشل في تصدير الإعدادات!")
    
    def import_settings(self):
        """استيراد الإعدادات"""
        file_path = filedialog.askopenfilename(
            title="استيراد الإعدادات",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            if self.settings_manager.import_settings(file_path):
                self.load_current_settings()
                messagebox.showinfo("تم", "تم استيراد الإعدادات بنجاح!")
            else:
                messagebox.showerror("خطأ", "فشل في استيراد الإعدادات!")
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        result = messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟")
        
        if result:
            self.settings_manager.reset_to_defaults()
            self.load_current_settings()
            messagebox.showinfo("تم", "تم إعادة تعيين الإعدادات!")
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"600x700+{x}+{y}")

def test_settings():
    """اختبار نافذة الإعدادات"""
    root = tk.Tk()
    root.withdraw()
    
    settings_manager = SettingsManager()
    settings_window = SettingsWindow(root, settings_manager)
    
    root.mainloop()

if __name__ == "__main__":
    test_settings()
