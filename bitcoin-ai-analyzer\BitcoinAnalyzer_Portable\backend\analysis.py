"""
Technical Analysis Module - وحدة التحليل الفني
حساب المؤشرات الفنية للبتكوين: RSI, MACD, EMA, Bollinger Bands, Volume Analysis
"""

import pandas as pd
import numpy as np
import ta
from ta.trend import EMAIndicator, MACD
from ta.momentum import RSIIndicator
from ta.volatility import BollingerBands
# استخدام pandas للمتوسط المتحرك للحجم
import logging

logger = logging.getLogger(__name__)

class TechnicalAnalyzer:
    """فئة التحليل الفني للبتكوين"""
    
    def __init__(self, df):
        """
        تهيئة المحلل الفني
        
        Args:
            df: DataFrame يحتوي على بيانات OHLCV
        """
        self.df = df.copy()
        self.indicators = {}
        
    def calculate_rsi(self, period=14):
        """
        حساب مؤشر القوة النسبية RSI
        
        Args:
            period: فترة الحساب (افتراضي 14)
        
        Returns:
            dict: قيم RSI مع التفسير
        """
        try:
            rsi_indicator = RSIIndicator(close=self.df['close'], window=period)
            rsi_values = rsi_indicator.rsi()
            
            current_rsi = rsi_values.iloc[-1]
            
            # تفسير RSI
            if current_rsi > 70:
                interpretation = "تشبع شرائي - إشارة بيع محتملة"
                signal = "bearish"
            elif current_rsi < 30:
                interpretation = "تشبع بيعي - إشارة شراء محتملة"
                signal = "bullish"
            else:
                interpretation = "منطقة متوازنة"
                signal = "neutral"
            
            self.indicators['rsi'] = {
                'value': round(current_rsi, 2),
                'interpretation': interpretation,
                'signal': signal,
                'history': rsi_values.tail(50).tolist()
            }
            
            logger.info(f"RSI محسوب: {current_rsi:.2f} - {interpretation}")
            return self.indicators['rsi']
            
        except Exception as e:
            logger.error(f"خطأ في حساب RSI: {e}")
            return None
    
    def calculate_macd(self, fast=12, slow=26, signal=9):
        """
        حساب مؤشر MACD
        
        Args:
            fast: فترة EMA السريع
            slow: فترة EMA البطيء
            signal: فترة خط الإشارة
        """
        try:
            macd_indicator = MACD(
                close=self.df['close'],
                window_fast=fast,
                window_slow=slow,
                window_sign=signal
            )
            
            macd_line = macd_indicator.macd()
            macd_signal = macd_indicator.macd_signal()
            macd_histogram = macd_indicator.macd_diff()
            
            current_macd = macd_line.iloc[-1]
            current_signal = macd_signal.iloc[-1]
            current_histogram = macd_histogram.iloc[-1]
            
            # تحديد التقاطع
            prev_macd = macd_line.iloc[-2]
            prev_signal = macd_signal.iloc[-2]
            
            if current_macd > current_signal and prev_macd <= prev_signal:
                cross_signal = "bullish"
                interpretation = "تقاطع صاعد - إشارة شراء"
            elif current_macd < current_signal and prev_macd >= prev_signal:
                cross_signal = "bearish"
                interpretation = "تقاطع هابط - إشارة بيع"
            else:
                if current_macd > current_signal:
                    cross_signal = "bullish"
                    interpretation = "MACD فوق خط الإشارة - اتجاه صاعد"
                else:
                    cross_signal = "bearish"
                    interpretation = "MACD تحت خط الإشارة - اتجاه هابط"
            
            self.indicators['macd'] = {
                'macd': round(current_macd, 2),
                'signal': round(current_signal, 2),
                'histogram': round(current_histogram, 2),
                'cross_signal': cross_signal,
                'interpretation': interpretation,
                'history': {
                    'macd': macd_line.tail(50).tolist(),
                    'signal': macd_signal.tail(50).tolist(),
                    'histogram': macd_histogram.tail(50).tolist()
                }
            }
            
            logger.info(f"MACD محسوب: {interpretation}")
            return self.indicators['macd']
            
        except Exception as e:
            logger.error(f"خطأ في حساب MACD: {e}")
            return None
    
    def calculate_ema(self, periods=[50, 200]):
        """
        حساب المتوسطات المتحركة الأسية
        
        Args:
            periods: قائمة بالفترات المطلوبة
        """
        try:
            emas = {}
            current_price = self.df['close'].iloc[-1]
            
            for period in periods:
                ema_indicator = EMAIndicator(close=self.df['close'], window=period)
                ema_values = ema_indicator.ema_indicator()
                current_ema = ema_values.iloc[-1]
                
                emas[f'ema_{period}'] = {
                    'value': round(current_ema, 2),
                    'history': ema_values.tail(50).tolist()
                }
            
            # تحليل الاتجاه
            if current_price > emas['ema_50']['value'] > emas['ema_200']['value']:
                trend = "bullish"
                interpretation = "اتجاه صاعد قوي - السعر فوق EMA 50 و 200"
            elif current_price < emas['ema_50']['value'] < emas['ema_200']['value']:
                trend = "bearish"
                interpretation = "اتجاه هابط قوي - السعر تحت EMA 50 و 200"
            else:
                trend = "neutral"
                interpretation = "اتجاه مختلط - لا يوجد اتجاه واضح"
            
            self.indicators['ema'] = {
                **emas,
                'trend': trend,
                'interpretation': interpretation,
                'current_price': round(current_price, 2)
            }
            
            logger.info(f"EMA محسوب: {interpretation}")
            return self.indicators['ema']
            
        except Exception as e:
            logger.error(f"خطأ في حساب EMA: {e}")
            return None

    def calculate_bollinger_bands(self, period=20, std_dev=2):
        """
        حساب نطاقات بولينجر

        Args:
            period: فترة المتوسط المتحرك
            std_dev: عدد الانحرافات المعيارية
        """
        try:
            bb_indicator = BollingerBands(
                close=self.df['close'],
                window=period,
                window_dev=std_dev
            )

            bb_upper = bb_indicator.bollinger_hband()
            bb_middle = bb_indicator.bollinger_mavg()
            bb_lower = bb_indicator.bollinger_lband()

            current_price = self.df['close'].iloc[-1]
            current_upper = bb_upper.iloc[-1]
            current_middle = bb_middle.iloc[-1]
            current_lower = bb_lower.iloc[-1]

            # تحليل الموقع
            bb_width = ((current_upper - current_lower) / current_middle) * 100

            if current_price >= current_upper:
                position = "above_upper"
                interpretation = "السعر فوق النطاق العلوي - تشبع شرائي محتمل"
                signal = "bearish"
            elif current_price <= current_lower:
                position = "below_lower"
                interpretation = "السعر تحت النطاق السفلي - تشبع بيعي محتمل"
                signal = "bullish"
            elif current_price > current_middle:
                position = "upper_half"
                interpretation = "السعر في النصف العلوي - ضغط شرائي"
                signal = "neutral_bullish"
            else:
                position = "lower_half"
                interpretation = "السعر في النصف السفلي - ضغط بيعي"
                signal = "neutral_bearish"

            self.indicators['bollinger'] = {
                'upper': round(current_upper, 2),
                'middle': round(current_middle, 2),
                'lower': round(current_lower, 2),
                'current_price': round(current_price, 2),
                'width': round(bb_width, 2),
                'position': position,
                'signal': signal,
                'interpretation': interpretation,
                'history': {
                    'upper': bb_upper.tail(50).tolist(),
                    'middle': bb_middle.tail(50).tolist(),
                    'lower': bb_lower.tail(50).tolist()
                }
            }

            logger.info(f"Bollinger Bands محسوب: {interpretation}")
            return self.indicators['bollinger']

        except Exception as e:
            logger.error(f"خطأ في حساب Bollinger Bands: {e}")
            return None

    def analyze_volume(self, period=20):
        """
        تحليل حجم التداول

        Args:
            period: فترة المتوسط المتحرك للحجم
        """
        try:
            # حساب المتوسط المتحرك للحجم باستخدام pandas
            volume_sma = self.df['volume'].rolling(window=period).mean()

            current_volume = self.df['volume'].iloc[-1]
            avg_volume = volume_sma.iloc[-1]
            volume_ratio = current_volume / avg_volume

            # تحليل الحجم
            if volume_ratio > 2:
                volume_signal = "very_high"
                interpretation = "حجم تداول عالي جداً - اهتمام قوي بالسوق"
            elif volume_ratio > 1.5:
                volume_signal = "high"
                interpretation = "حجم تداول عالي - نشاط متزايد"
            elif volume_ratio > 0.8:
                volume_signal = "normal"
                interpretation = "حجم تداول طبيعي"
            else:
                volume_signal = "low"
                interpretation = "حجم تداول منخفض - اهتمام ضعيف"

            # تحليل اتجاه السعر مع الحجم
            price_change = (self.df['close'].iloc[-1] - self.df['close'].iloc[-2]) / self.df['close'].iloc[-2] * 100

            if price_change > 0 and volume_ratio > 1.2:
                volume_trend = "bullish_confirmation"
                trend_interpretation = "ارتفاع السعر مع حجم عالي - تأكيد صاعد"
            elif price_change < 0 and volume_ratio > 1.2:
                volume_trend = "bearish_confirmation"
                trend_interpretation = "انخفاض السعر مع حجم عالي - تأكيد هابط"
            else:
                volume_trend = "weak_signal"
                trend_interpretation = "إشارة ضعيفة - حجم غير مؤكد للاتجاه"

            self.indicators['volume'] = {
                'current': int(current_volume),
                'average': int(avg_volume),
                'ratio': round(volume_ratio, 2),
                'signal': volume_signal,
                'trend': volume_trend,
                'interpretation': interpretation,
                'trend_interpretation': trend_interpretation,
                'history': self.df['volume'].tail(50).tolist()
            }

            logger.info(f"Volume محلل: {interpretation}")
            return self.indicators['volume']

        except Exception as e:
            logger.error(f"خطأ في تحليل الحجم: {e}")
            return None

    def calculate_all_indicators(self):
        """حساب جميع المؤشرات الفنية"""
        try:
            logger.info("بدء حساب جميع المؤشرات الفنية...")

            # حساب جميع المؤشرات
            self.calculate_rsi()
            self.calculate_macd()
            self.calculate_ema()
            self.calculate_bollinger_bands()
            self.analyze_volume()

            logger.info("تم حساب جميع المؤشرات بنجاح")
            return self.indicators

        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات: {e}")
            return None

    def get_summary(self):
        """ملخص سريع لجميع المؤشرات"""
        if not self.indicators:
            self.calculate_all_indicators()

        summary = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'current_price': self.df['close'].iloc[-1],
            'indicators_count': len(self.indicators),
            'signals': {}
        }

        # جمع الإشارات
        for indicator, data in self.indicators.items():
            if 'signal' in data:
                summary['signals'][indicator] = data['signal']

        return summary

def test_technical_analysis():
    """اختبار التحليل الفني"""
    from utils import BitcoinDataFetcher

    print("🔍 اختبار التحليل الفني...")

    # جلب البيانات
    fetcher = BitcoinDataFetcher()
    df = fetcher.get_klines('1h', 200)

    if df is None or df.empty:
        print("❌ فشل في جلب البيانات")
        return

    # إجراء التحليل
    analyzer = TechnicalAnalyzer(df)
    indicators = analyzer.calculate_all_indicators()

    if indicators:
        print("✅ تم حساب المؤشرات بنجاح:")
        for name, data in indicators.items():
            print(f"📊 {name.upper()}: {data.get('interpretation', 'غير متوفر')}")
    else:
        print("❌ فشل في حساب المؤشرات")

if __name__ == "__main__":
    test_technical_analysis()
