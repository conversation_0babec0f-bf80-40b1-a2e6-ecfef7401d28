/**
 * Bitcoin AI Analyzer - Frontend JavaScript
 * التفاعل مع API وعرض البيانات والرسوم البيانية
 */

class BitcoinAnalyzer {
    constructor() {
        this.apiBaseUrl = 'http://localhost:5000/api';
        this.chart = null;
        this.currentTimeframe = '1h';
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupTheme();
        this.loadInitialData();
    }
    
    setupEventListeners() {
        // زر التحديث
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.analyzeMarket();
        });
        
        // زر إعادة المحاولة
        document.getElementById('retryBtn').addEventListener('click', () => {
            this.hideError();
            this.loadInitialData();
        });
        
        // تبديل الوضع الليلي
        document.getElementById('darkModeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });
        
        // تغيير الفترة الزمنية
        document.getElementById('timeframeSelect').addEventListener('change', (e) => {
            this.currentTimeframe = e.target.value;
            this.updateChart();
        });
        
        // تحديث تلقائي كل 5 دقائق
        setInterval(() => {
            if (!this.isLoading) {
                this.updatePriceData();
            }
        }, 300000); // 5 دقائق
    }
    
    setupTheme() {
        // تحديد الثيم المحفوظ أو الافتراضي
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        const darkModeIcon = document.querySelector('#darkModeToggle i');
        if (savedTheme === 'dark') {
            darkModeIcon.className = 'fas fa-sun';
        } else {
            darkModeIcon.className = 'fas fa-moon';
        }
    }
    
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        const darkModeIcon = document.querySelector('#darkModeToggle i');
        if (newTheme === 'dark') {
            darkModeIcon.className = 'fas fa-sun';
        } else {
            darkModeIcon.className = 'fas fa-moon';
        }
        
        // تحديث الرسم البياني إذا كان موجوداً
        if (this.chart) {
            this.updateChartTheme();
        }
    }
    
    async loadInitialData() {
        this.showLoading();
        
        try {
            // تحميل البيانات الأساسية
            await Promise.all([
                this.updatePriceData(),
                this.analyzeMarket(),
                this.loadHistory()
            ]);
            
            this.hideLoading();
        } catch (error) {
            console.error('خطأ في تحميل البيانات الأولية:', error);
            this.showError('فشل في تحميل البيانات الأولية');
            this.hideLoading();
        }
    }
    
    async updatePriceData() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/current-price`);
            const data = await response.json();
            
            if (response.ok) {
                this.displayPriceData(data);
            } else {
                throw new Error(data.error || 'فشل في جلب بيانات السعر');
            }
        } catch (error) {
            console.error('خطأ في تحديث السعر:', error);
            // لا نعرض خطأ للتحديث التلقائي
        }
    }
    
    async analyzeMarket() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/analyze?timeframe=${this.currentTimeframe}`);
            const data = await response.json();
            
            if (response.ok) {
                this.displayAnalysisResults(data);
                this.updateChart();
            } else {
                throw new Error(data.error || 'فشل في تحليل السوق');
            }
        } catch (error) {
            console.error('خطأ في التحليل:', error);
            this.showError(error.message);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }
    
    displayPriceData(data) {
        // السعر الحالي
        document.getElementById('currentPrice').textContent = 
            `$${this.formatNumber(data.price)}`;
        
        // التغيير في 24 ساعة
        const priceChangeElement = document.getElementById('priceChange');
        const changePercent = data.price_change_percent || 0;
        
        priceChangeElement.innerHTML = `
            <i class="fas fa-arrow-${changePercent >= 0 ? 'up' : 'down'}"></i>
            ${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%
        `;
        
        priceChangeElement.className = `price-change ${changePercent >= 0 ? 'positive' : 'negative'}`;
        
        // إحصائيات 24 ساعة
        if (data.high_price) {
            document.getElementById('high24h').textContent = `$${this.formatNumber(data.high_price)}`;
        }
        if (data.low_price) {
            document.getElementById('low24h').textContent = `$${this.formatNumber(data.low_price)}`;
        }
        if (data.volume) {
            document.getElementById('volume24h').textContent = `${this.formatNumber(data.volume, 0)} BTC`;
        }
        
        // آخر تحديث
        document.getElementById('lastUpdate').textContent = 
            `آخر تحديث: ${new Date().toLocaleTimeString('ar-SA')}`;
    }
    
    displayAnalysisResults(data) {
        // التوصية الرئيسية
        this.displayRecommendation(data);
        
        // المؤشرات الفنية
        this.displayIndicators(data.individual_signals);
        
        // معنويات السوق
        this.displayMarketSentiment(data.market_sentiment);
    }
    
    displayRecommendation(data) {
        const recommendation = data.recommendation;
        const confidence = data.confidence;
        
        // أيقونة التوصية
        const iconElement = document.getElementById('recommendationIcon');
        const actionElement = document.getElementById('recommendationAction');
        const explanationElement = document.getElementById('recommendationExplanation');
        
        // تحديد اللون والأيقونة
        let iconClass = 'fas fa-clock';
        let colorClass = '';
        
        if (recommendation.includes('شراء')) {
            iconClass = 'fas fa-arrow-trend-up';
            colorClass = 'buy';
        } else if (recommendation.includes('بيع')) {
            iconClass = 'fas fa-arrow-trend-down';
            colorClass = 'sell';
        }
        
        iconElement.innerHTML = `<i class="${iconClass}"></i>`;
        iconElement.className = `recommendation-icon ${colorClass}`;
        
        actionElement.textContent = recommendation;
        explanationElement.textContent = this.extractMainExplanation(data.explanation);
        
        // مستوى الثقة
        const confidencePercent = Math.round(confidence * 100);
        document.getElementById('confidenceValue').textContent = `${confidencePercent}%`;
        document.getElementById('confidenceFill').style.width = `${confidencePercent}%`;
    }
    
    displayIndicators(signals) {
        if (!signals) return;
        
        // RSI
        if (signals.rsi) {
            const rsiData = signals.rsi;
            document.getElementById('rsiValue').textContent = rsiData.score;
            document.getElementById('rsiInterpretation').textContent = rsiData.explanation;
            
            // تحديث شريط RSI (افتراض أن القيمة بين 0-100)
            const rsiPercent = Math.max(0, Math.min(100, rsiData.score));
            document.getElementById('rsiFill').style.width = `${rsiPercent}%`;
            document.getElementById('rsiPointer').style.left = `${rsiPercent}%`;
        }
        
        // MACD
        if (signals.macd) {
            const macdData = signals.macd;
            document.getElementById('macdInterpretation').textContent = macdData.explanation;
        }
        
        // EMA
        if (signals.ema) {
            const emaData = signals.ema;
            document.getElementById('emaInterpretation').textContent = emaData.explanation;
        }
        
        // Bollinger Bands
        if (signals.bollinger) {
            const bbData = signals.bollinger;
            document.getElementById('bbInterpretation').textContent = bbData.explanation;
        }
        
        // Volume
        if (signals.volume) {
            const volumeData = signals.volume;
            document.getElementById('volumeInterpretation').textContent = volumeData.explanation;
        }
    }
    
    displayMarketSentiment(sentiment) {
        if (!sentiment) return;
        
        const sentimentElement = document.getElementById('marketSentiment');
        sentimentElement.innerHTML = `
            <span class="sentiment-emoji">${sentiment.emoji}</span>
            ${sentiment.sentiment}
        `;
        sentimentElement.style.color = sentiment.color;
    }
    
    async updateChart() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/chart-data?timeframe=${this.currentTimeframe}&limit=100`);
            const data = await response.json();

            if (response.ok) {
                this.renderChart(data.data);
            }
        } catch (error) {
            console.error('خطأ في تحديث الرسم البياني:', error);
        }
    }

    renderChart(chartData) {
        const ctx = document.getElementById('priceChart').getContext('2d');

        // تحضير البيانات
        const labels = chartData.map(item => new Date(item.timestamp));
        const prices = chartData.map(item => item.close);
        const volumes = chartData.map(item => item.volume);

        // تدمير الرسم البياني السابق إذا كان موجوداً
        if (this.chart) {
            this.chart.destroy();
        }

        // إنشاء الرسم البياني الجديد
        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'سعر البتكوين (USD)',
                    data: prices,
                    borderColor: '#f7931a',
                    backgroundColor: 'rgba(247, 147, 26, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: true,
                        labels: {
                            color: this.getTextColor(),
                            font: {
                                family: 'Cairo'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#f7931a',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return `السعر: $${context.parsed.y.toLocaleString()}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                hour: 'HH:mm',
                                day: 'MMM dd'
                            }
                        },
                        grid: {
                            color: this.getGridColor()
                        },
                        ticks: {
                            color: this.getTextColor()
                        }
                    },
                    y: {
                        grid: {
                            color: this.getGridColor()
                        },
                        ticks: {
                            color: this.getTextColor(),
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    updateChartTheme() {
        if (!this.chart) return;

        // تحديث ألوان الرسم البياني حسب الثيم
        this.chart.options.plugins.legend.labels.color = this.getTextColor();
        this.chart.options.scales.x.grid.color = this.getGridColor();
        this.chart.options.scales.y.grid.color = this.getGridColor();
        this.chart.options.scales.x.ticks.color = this.getTextColor();
        this.chart.options.scales.y.ticks.color = this.getTextColor();

        this.chart.update();
    }

    async loadHistory() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/history?limit=10`);
            const data = await response.json();

            if (response.ok) {
                this.displayHistory(data.analyses);
            }
        } catch (error) {
            console.error('خطأ في تحميل التاريخ:', error);
        }
    }

    displayHistory(analyses) {
        const container = document.getElementById('historyContainer');

        if (!analyses || analyses.length === 0) {
            container.innerHTML = '<p class="no-data">لا توجد تحليلات سابقة</p>';
            return;
        }

        const historyHTML = analyses.map(analysis => {
            const date = new Date(analysis.timestamp);
            const recommendation = analysis.recommendation;
            const confidence = Math.round(analysis.confidence * 100);

            let recommendationClass = 'wait';
            if (recommendation.includes('شراء')) {
                recommendationClass = 'buy';
            } else if (recommendation.includes('بيع')) {
                recommendationClass = 'sell';
            }

            return `
                <div class="history-item fade-in">
                    <div class="history-info">
                        <span class="history-recommendation ${recommendationClass}">
                            ${recommendation}
                        </span>
                        <span class="history-time">
                            ${date.toLocaleDateString('ar-SA')} - ${date.toLocaleTimeString('ar-SA')}
                        </span>
                    </div>
                    <div class="history-details">
                        <span>الثقة: ${confidence}%</span>
                        <span>السعر: $${this.formatNumber(analysis.price)}</span>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = historyHTML;
    }

    // وظائف مساعدة
    showLoading() {
        document.getElementById('loadingIndicator').classList.add('show');
    }

    hideLoading() {
        document.getElementById('loadingIndicator').classList.remove('show');
    }

    showError(message) {
        document.getElementById('errorText').textContent = message;
        document.getElementById('errorMessage').style.display = 'flex';
    }

    hideError() {
        document.getElementById('errorMessage').style.display = 'none';
    }

    formatNumber(number, decimals = 2) {
        if (typeof number !== 'number') return '--';
        return number.toLocaleString('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }

    extractMainExplanation(fullExplanation) {
        if (!fullExplanation) return 'جاري التحليل...';

        // استخراج الجملة الأولى من التفسير
        const lines = fullExplanation.split('\n');
        for (let line of lines) {
            if (line.includes('**التحليل الإجمالي:**')) {
                return line.replace('📊 **التحليل الإجمالي:**', '').trim();
            }
        }

        // إذا لم نجد التحليل الإجمالي، نأخذ أول سطر مفيد
        for (let line of lines) {
            if (line.trim() && !line.includes('🎯') && !line.includes('**')) {
                return line.trim();
            }
        }

        return 'تحليل السوق جاري...';
    }

    getTextColor() {
        const theme = document.documentElement.getAttribute('data-theme');
        return theme === 'dark' ? '#ffffff' : '#212529';
    }

    getGridColor() {
        const theme = document.documentElement.getAttribute('data-theme');
        return theme === 'dark' ? '#444444' : '#dee2e6';
    }
}

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new BitcoinAnalyzer();
});

// معالجة الأخطاء العامة
window.addEventListener('error', (event) => {
    console.error('خطأ في التطبيق:', event.error);
});

// معالجة الأخطاء في الشبكة
window.addEventListener('unhandledrejection', (event) => {
    console.error('خطأ في الشبكة:', event.reason);
});
